// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {
            "name": "RPay Development Team",
            "email": "<EMAIL>"
        },
        "license": {
            "name": "Proprietary",
            "url": "https://rpay.com/license"
        },
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/api/v1/barcode/generate-seed": {
            "post": {
                "security": [
                    {
                        "BearerAuth": []
                    }
                ],
                "description": "Generates an offline barcode seed for a shopper. This endpoint creates or retrieves barcode seed details that can be used for offline barcode generation.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Barcode"
                ],
                "summary": "Generate offline barcode seed",
                "parameters": [
                    {
                        "type": "string",
                        "default": "Bearer ",
                        "description": "Bearer JWT token",
                        "name": "Authorization",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Barcode generation request",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/http.BarcodeGenerateSeedRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Barcode seed generated successfully",
                        "schema": {
                            "$ref": "#/definitions/http.BarcodeGenerateSeedResponse"
                        }
                    },
                    "400": {
                        "description": "Invalid request format",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "401": {
                        "description": "Unauthorized - Invalid or missing JWT token",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "403": {
                        "description": "Forbidden - Insufficient permissions",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    },
                    "500": {
                        "description": "Internal server error",
                        "schema": {
                            "$ref": "#/definitions/http.ErrorResponse"
                        }
                    }
                }
            }
        },
        "/health": {
            "get": {
                "description": "Returns the health status of the service",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "Health"
                ],
                "summary": "Health check endpoint",
                "responses": {
                    "200": {
                        "description": "Service is healthy",
                        "schema": {
                            "$ref": "#/definitions/http.HealthResponse"
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "http.BarcodeGenerateSeedRequest": {
            "type": "object",
            "required": [
                "deviceId",
                "easyId",
                "ipAddress",
                "sessionId"
            ],
            "properties": {
                "deviceId": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "device123"
                },
                "easyId": {
                    "type": "integer",
                    "minimum": 1,
                    "example": 123456
                },
                "ipAddress": {
                    "type": "string",
                    "example": "***********"
                },
                "sessionId": {
                    "type": "string",
                    "maxLength": 255,
                    "minLength": 1,
                    "example": "session123"
                }
            }
        },
        "http.BarcodeGenerateSeedResponse": {
            "type": "object",
            "properties": {
                "keyId": {
                    "type": "string",
                    "example": "12345678901"
                },
                "minExpiryDate": {
                    "type": "string",
                    "example": "2024-01-15T10:30:00Z"
                },
                "otpSeed": {
                    "type": "string",
                    "example": "JBSWY3DPEHPK3PXP"
                }
            }
        },
        "http.ErrorResponse": {
            "type": "object",
            "properties": {
                "error": {
                    "type": "string",
                    "example": "INVALID_REQUEST"
                },
                "message": {
                    "type": "string",
                    "example": "Invalid request format"
                }
            }
        },
        "http.HealthResponse": {
            "type": "object",
            "properties": {
                "service": {
                    "type": "string",
                    "example": "rpay-barcode-service-go"
                },
                "status": {
                    "type": "string",
                    "example": "UP"
                }
            }
        }
    },
    "securityDefinitions": {
        "BearerAuth": {
            "description": "JWT token for authentication. The token should contain: Subject (sub): Must match configured RPAY_SUBJECT, Roles: Must include configured BARCODE_INTERNAL_ROLE, Expiration (exp): Token must not be expired",
            "type": "apiKey",
            "name": "Authorization",
            "in": "header"
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0.0",
	Host:             "localhost:8081",
	BasePath:         "/",
	Schemes:          []string{"http"},
	Title:            "RPay Barcode Service API",
	Description:      "RPay Barcode Service provides APIs for generating and verifying offline barcode seeds.\n\n## Authentication\nAll API endpoints (except health check) require JWT authentication using Bearer token.\n\n## Features\n- Generate offline barcode seeds with OTP\n- JWT-based authentication\n- Redis caching for improved performance\n- Health monitoring\n\n## Rate Limiting\nAPI calls are subject to rate limiting to ensure service stability.",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
