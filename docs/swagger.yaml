openapi: 3.0.3
info:
  title: RPay Barcode Service API
  description: |
    RPay Barcode Service provides APIs for generating and verifying offline barcode seeds.
    
    ## Authentication
    All API endpoints (except health check) require JWT authentication using Bearer token.
    
    ## Features
    - Generate offline barcode seeds with OTP
    - JWT-based authentication
    - Redis caching for improved performance
    - Health monitoring
    
    ## Rate Limiting
    API calls are subject to rate limiting to ensure service stability.
  version: 1.0.0
  contact:
    name: RPay Development Team
    email: <EMAIL>
  license:
    name: Proprietary
    url: https://rpay.com/license

servers:
  - url: http://localhost:8081
    description: Development server
  - url: https://api.rpay.com
    description: Production server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check endpoint
      description: Returns the health status of the service
      security: []
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: "UP"
                  service:
                    type: string
                    example: "rpay-barcode-service-go"
              example:
                status: "UP"
                service: "rpay-barcode-service-go"

  /api/v1/barcode/generate-seed:
    post:
      tags:
        - Barcode
      summary: Generate offline barcode seed
      description: |
        Generates an offline barcode seed for a shopper. This endpoint creates or retrieves
        barcode seed details that can be used for offline barcode generation.
        
        ## Caching
        Results are cached in Redis for improved performance on subsequent requests.
        
        ## Business Logic
        - If valid existing seed exists and hasn't expired, returns existing seed
        - If no seed exists or existing seed is expired, generates new seed
        - Uses Argon2 for secure OTP seed generation
        - Implements deterministic key generation
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BarcodeGenerateSeedRequest'
            example:
              easyId: 123456
              deviceId: "device123"
              sessionId: "session123"
              ipAddress: "***********"
      responses:
        '200':
          description: Barcode seed generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BarcodeGenerateSeedResponse'
              example:
                keyId: "***********"
                otpSeed: "JBSWY3DPEHPK3PXP"
                minExpiryDate: "2024-01-15T10:30:00Z"
        '400':
          description: Invalid request format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "INVALID_REQUEST"
                message: "Invalid request format: missing required field 'easyId'"
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "UNAUTHORIZED"
                message: "Invalid or missing JWT token"
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "FORBIDDEN"
                message: "Insufficient permissions for this operation"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "GENERATION_FAILED"
                message: "Failed to generate offline details: database connection error"

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: |
        JWT token for authentication. The token should contain:
        - Subject (sub): Must match configured RPAY_SUBJECT
        - Roles: Must include configured BARCODE_INTERNAL_ROLE
        - Expiration (exp): Token must not be expired

  schemas:
    BarcodeGenerateSeedRequest:
      type: object
      required:
        - easyId
        - deviceId
        - sessionId
        - ipAddress
      properties:
        easyId:
          type: integer
          format: int64
          description: Unique identifier for the shopper
          example: 123456
          minimum: 1
        deviceId:
          type: string
          description: Unique identifier for the device
          example: "device123"
          minLength: 1
          maxLength: 255
        sessionId:
          type: string
          description: Session identifier
          example: "session123"
          minLength: 1
          maxLength: 255
        ipAddress:
          type: string
          description: IP address of the client
          example: "***********"
          pattern: '^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'

    BarcodeGenerateSeedResponse:
      type: object
      properties:
        keyId:
          type: string
          description: Generated key identifier for the barcode seed
          example: "***********"
          minLength: 11
          maxLength: 11
        otpSeed:
          type: string
          description: Base32-encoded OTP seed for barcode generation
          example: "JBSWY3DPEHPK3PXP"
          pattern: '^[A-Z2-7]+=*$'
        minExpiryDate:
          type: string
          format: date-time
          description: Minimum expiry date for the barcode seed
          example: "2024-01-15T10:30:00Z"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error code
          example: "INVALID_REQUEST"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid request format"

tags:
  - name: Health
    description: Health monitoring endpoints
  - name: Barcode
    description: Barcode generation and management endpoints
