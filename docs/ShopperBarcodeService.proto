syntax = "proto3";

option java_package = "jp.co.rakuten.barcode.grpc.generated";

option java_multiple_files = true;

message BarcodeGenerateSeedRequest {
    int64 easyId = 1;
    string deviceId = 2;
    string sessionId = 3;
    string ipAddress = 4;
}

message BarcodeGenerateSeedResponse {
    string keyId = 1;
    string otpSeed = 2;
    string minExpiryDate = 3;
}

service ShopperBarcodeService {
  rpc GetShopperBarcodeGenerateSeed(BarcodeGenerateSeedRequest) returns (BarcodeGenerateSeedResponse);
}