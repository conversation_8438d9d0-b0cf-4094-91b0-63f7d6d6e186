syntax = "proto3";

option java_package = "jp.co.rakuten.barcode.grpc.generated";

option java_multiple_files = true;

message BarcodeVerifyOtpRequest {
  string offlineBarcode = 1;
}

message BarcodeVerifyOtpResponse {
  bool status = 1;
  optional string barcode = 2;
  optional int64 barcodeId = 3;
  optional int64 easyId = 4;
  optional string deviceId = 5;
  optional string ipAddress = 6;
}

service ShopperBarcodeInternalService {
  rpc GetShopperBarcodeVerifyOtp(BarcodeVerifyOtpRequest) returns (BarcodeVerifyOtpResponse);
}