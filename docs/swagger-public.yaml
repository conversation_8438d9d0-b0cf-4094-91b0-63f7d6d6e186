openapi: 3.0.3
info:
  title: RPay Barcode Service Public API
  description: |
    This is for external integrations to generate offline barcode seeds for shoppers.
    Calls to Shopper Router and then to the current service.
    App--[this]-->Shopper Router->RPay Barcode Service
  version: 1.0.0
  contact:
    name: yuhoi.chau
    email: <EMAIL>

servers:
  - url: http://localhost:8081
    description: Development server

security:
  - RtokenAuth: []

paths:
  /engine/api/ShopperOfflineBarcode:
    post:
      tags:
        - Barcode
      summary: Generate offline barcode seed for shopper
      description: |
        Generates an offline barcode seed for a shopper. This endpoint creates or retrieves
        barcode seed details that can be used for offline barcode generation.
        
        ## Caching
        Results are cached in Redis for improved performance on subsequent requests.
        
        ## Business Logic
        - If valid existing seed exists and hasn't expired, returns existing seed
        - If no seed exists or existing seed is expired, generates new seed
        - Uses Argon2 for secure OTP seed generation
        - Implements deterministic key generation
      security:
        - RtokenAuth: []
      parameters:
        - name: <PERSON><PERSON>en
          in: header
          required: true
          description: Bearer token for authentication
          schema:
            type: string
            example: "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShopperOfflineBarcodeRequest'
            example:
              easyId: 123456
              deviceId: "device123"
              sessionId: "session123"
              ipAddress: "***********"
      responses:
        '200':
          description: Barcode seed generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShopperOfflineBarcodeResponse'
              example:
                keyId: "12345678901"
                otpSeed: "JBSWY3DPEHPK3PXP"
                minExpiryDate: "2024-01-15T10:30:00Z"
        '400':
          description: Invalid request format
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "INVALID_REQUEST"
                message: "Invalid request format: missing required field 'easyId'"
        '401':
          description: Unauthorized - Invalid or missing Rtoken
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "UNAUTHORIZED"
                message: "Invalid or missing Rtoken header"
        '403':
          description: Forbidden - Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "FORBIDDEN"
                message: "Insufficient permissions for this operation"
        '500':
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
              example:
                error: "GENERATION_FAILED"
                message: "Failed to generate offline details: database connection error"

components:
  securitySchemes:
    RtokenAuth:
      type: http
      scheme: bearer
      bearerFormat: GlassToken
      description: |
        <TBD> ... rtoken will be translated to JWT internally by Shopper Router(?)

  schemas:
    ShopperOfflineBarcodeRequest:
      type: object
      required:
        - easyId
        - deviceId
        - sessionId
        - ipAddress
      properties:
        easyId:
          type: integer
          format: int64
          description: Unique identifier for the shopper
          example: 123456
          minimum: 1
        deviceId:
          type: string
          description: Unique identifier for the device
          example: "device123"
          minLength: 1
          maxLength: 255
        sessionId:
          type: string
          description: Session identifier
          example: "session123"
          minLength: 1
          maxLength: 255
        ipAddress:
          type: string
          description: IP address of the client
          example: "***********"
          pattern: '^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$'

    ShopperOfflineBarcodeResponse:
      type: object
      properties:
        keyId:
          type: string
          description: Generated key identifier for the barcode seed
          example: "12345678901"
          minLength: 11
          maxLength: 11
        otpSeed:
          type: string
          description: Base32-encoded OTP seed for barcode generation
          example: "JBSWY3DPEHPK3PXP"
          pattern: '^[A-Z2-7]+=*$'
        minExpiryDate:
          type: string
          format: date-time
          description: Minimum expiry date for the barcode seed
          example: "2024-01-15T10:30:00Z"

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          description: Error code
          example: "INVALID_REQUEST"
        message:
          type: string
          description: Human-readable error message
          example: "Invalid request format"

tags:
  - name: Barcode
    description: Barcode generation and management endpoints for external integrations
