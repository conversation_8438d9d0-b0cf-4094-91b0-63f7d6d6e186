package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	pb "rpay-barcode-service-go/proto"
)

func main() {
	// Connect to the gRPC server
	conn, err := grpc.Dial("localhost:8080", grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer conn.Close()

	// Create clients
	shopperClient := pb.NewShopperBarcodeServiceClient(conn)
	internalClient := pb.NewShopperBarcodeInternalServiceClient(conn)

	// Generate JWT token
	secretKey := "dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU="
	token := generateJWT(secretKey, "rpay", []string{"barcode"})

	// Create context with JWT token
	ctx := metadata.AppendToOutgoingContext(context.Background(), "authorization", "Bearer "+token)

	// Test 1: Generate barcode seed
	fmt.Println("=== Testing GetShopperBarcodeGenerateSeed ===")
	seedReq := &pb.BarcodeGenerateSeedRequest{
		EasyId:    123456,
		DeviceId:  "device123",
		SessionId: "session123",
		IpAddress: "***********",
	}

	seedResp, err := shopperClient.GetShopperBarcodeGenerateSeed(ctx, seedReq)
	if err != nil {
		log.Printf("Error generating seed: %v", err)
	} else {
		fmt.Printf("Generated seed response:\n")
		fmt.Printf("  KeyId: %s\n", seedResp.KeyId)
		fmt.Printf("  OtpSeed: %s\n", seedResp.OtpSeed)
		fmt.Printf("  MinExpiryDate: %s\n", seedResp.MinExpiryDate)
	}

	// Test 2: Verify OTP (this will fail because we don't have a valid offline barcode)
	fmt.Println("\n=== Testing GetShopperBarcodeVerifyOtp ===")
	verifyReq := &pb.BarcodeVerifyOtpRequest{
		OfflineBarcode: "800001123456789012345678", // Example offline barcode
	}

	verifyResp, err := internalClient.GetShopperBarcodeVerifyOtp(ctx, verifyReq)
	if err != nil {
		log.Printf("Error verifying OTP: %v", err)
	} else {
		fmt.Printf("OTP verification response:\n")
		fmt.Printf("  Status: %t\n", verifyResp.Status)
		if verifyResp.Barcode != nil {
			fmt.Printf("  Barcode: %s\n", *verifyResp.Barcode)
		}
		if verifyResp.BarcodeId != nil {
			fmt.Printf("  BarcodeId: %d\n", *verifyResp.BarcodeId)
		}
	}

	fmt.Println("\n=== Test completed ===")
}

func generateJWT(secretKeyBase64, subject string, groups []string) string {
	// Decode the secret key
	secretKey, err := base64.StdEncoding.DecodeString(secretKeyBase64)
	if err != nil {
		log.Fatalf("Failed to decode secret key: %v", err)
	}

	// Create the claims
	claims := jwt.MapClaims{
		"sub":    subject,
		"groups": groups,
		"exp":    time.Now().Add(time.Hour).Unix(),
		"iat":    time.Now().Unix(),
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign the token
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		log.Fatalf("Failed to sign token: %v", err)
	}

	return tokenString
}
