package main

import (
	"context"
	"log"
	"net"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"rpay-barcode-service-go/pkg/cache"
	"rpay-barcode-service-go/pkg/config"
	"rpay-barcode-service-go/pkg/database"
	"rpay-barcode-service-go/pkg/http"
	"rpay-barcode-service-go/pkg/interceptor"
	"rpay-barcode-service-go/pkg/model"
	"rpay-barcode-service-go/pkg/service"
	pb "rpay-barcode-service-go/proto"
)

// SimpleMockDatabase is a minimal mock database for testing
type SimpleMockDatabase struct{}

func (m *SimpleMockDatabase) FindBarcodeSeedDetailsByKeyID(keyID string) (*model.BarcodeSeedDetails, error) {
	return nil, nil // Not found
}

func (m *SimpleMockDatabase) FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	return nil, nil // Not found
}

func (m *SimpleMockDatabase) CreateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	return nil // Success
}

func (m *SimpleMockDatabase) UpdateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	return nil // Success
}

func (m *SimpleMockDatabase) CreateBarcodeForShopper(bfs *model.BarcodeForShopper) (int64, error) {
	return 1, nil // Success
}

func (m *SimpleMockDatabase) CreateOfflineBarcodeTransaction(obt *model.OfflineBarcodeTransaction) (int64, error) {
	return 1, nil // Success
}

// initializeDatabase creates a database with optional Redis caching
func initializeDatabase(cfg *config.Config) database.DatabaseInterface {
	// Create MySQL database connection
	mysqlDB, err := database.NewMySQLDB(cfg.DatabaseDSN)
	if err != nil {
		log.Printf("Warning: Failed to connect to MySQL database: %v", err)
		log.Printf("Using mock database for testing purposes")
		// Return a mock database for testing when MySQL is not available
		return &SimpleMockDatabase{}
	}

	// If caching is disabled, return MySQL database directly
	if !cfg.EnableCache {
		log.Printf("Cache disabled, using MySQL database only")
		return mysqlDB
	}

	// Initialize Redis cache
	var cacheImpl cache.CacheInterface
	if cfg.EnableCache {
		redisCache := cache.NewRedisCache(cfg.RedisPrimaryAddr, cfg.RedisSecondaryAddr, cfg.RedisPassword, cfg.RedisDB, cfg.RedisTTL)
		cacheImpl = redisCache
	} else {
		cacheImpl = cache.NewNoOpCache()
	}

	// Create cached database wrapper
	cachedDB := database.NewCachedDB(mysqlDB, cacheImpl)
	log.Printf("Initialized MySQL database with dual Redis cache (primary: %s, secondary: %s, ttl: %v)",
		cfg.RedisPrimaryAddr, cfg.RedisSecondaryAddr, cfg.RedisTTL)
	return cachedDB
}

func main() {
	// Load configuration
	cfg := config.LoadConfig()
	log.Printf("Starting dual-mode server (gRPC + HTTP)...")

	// Initialize database with caching
	db := initializeDatabase(cfg)

	// Initialize business logic
	shopperLogic := service.NewShopperLogic(cfg, db)

	// Create wait group for concurrent servers
	var wg sync.WaitGroup

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start gRPC server
	wg.Add(1)
	go func() {
		defer wg.Done()
		startGRPCServer(cfg, shopperLogic, ctx)
	}()

	// Start HTTP server
	wg.Add(1)
	go func() {
		defer wg.Done()
		startHTTPServer(cfg, shopperLogic, ctx)
	}()

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Block until signal received
	<-sigChan
	log.Println("Shutdown signal received, stopping servers...")

	// Cancel context to stop servers
	cancel()

	// Wait for servers to stop
	wg.Wait()
	log.Println("All servers stopped")
}

func startGRPCServer(cfg *config.Config, shopperLogic *service.ShopperLogic, ctx context.Context) {
	// Initialize JWT interceptor
	jwtInterceptor := interceptor.NewJWTInterceptor(cfg)

	// Create gRPC server with interceptors
	server := grpc.NewServer(
		grpc.UnaryInterceptor(jwtInterceptor.UnaryServerInterceptor()),
	)

	// Register services
	shopperBarcodeService := service.NewShopperBarcodeServiceServer(shopperLogic)
	pb.RegisterShopperBarcodeServiceServer(server, shopperBarcodeService)

	shopperBarcodeInternalService := service.NewShopperBarcodeInternalServiceServer(shopperLogic)
	pb.RegisterShopperBarcodeInternalServiceServer(server, shopperBarcodeInternalService)

	// Enable reflection for testing with tools like grpcurl
	reflection.Register(server)

	// Create listener
	lis, err := net.Listen("tcp", ":"+cfg.Port)
	if err != nil {
		log.Fatalf("Failed to listen on gRPC port: %v", err)
	}

	log.Printf("gRPC server listening on :%s", cfg.Port)
	log.Printf("gRPC services registered:")
	log.Printf("  - ShopperBarcodeService")
	log.Printf("  - ShopperBarcodeInternalService")

	// Start server in a goroutine
	go func() {
		if err := server.Serve(lis); err != nil {
			log.Printf("gRPC server error: %v", err)
		}
	}()

	// Wait for context cancellation
	<-ctx.Done()
	log.Println("Stopping gRPC server...")

	// Graceful shutdown with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	done := make(chan struct{})
	go func() {
		server.GracefulStop()
		close(done)
	}()

	select {
	case <-done:
		log.Println("gRPC server stopped gracefully")
	case <-shutdownCtx.Done():
		log.Println("gRPC server shutdown timeout, forcing stop")
		server.Stop()
	}
}

func startHTTPServer(cfg *config.Config, shopperLogic *service.ShopperLogic, ctx context.Context) {
	// Create HTTP server
	httpServer := http.NewHTTPServer(cfg, shopperLogic)

	// Start server in a goroutine
	go func() {
		if err := httpServer.Start(); err != nil {
			log.Printf("HTTP server error: %v", err)
		}
	}()

	// Wait for context cancellation
	<-ctx.Done()
	log.Println("Stopping HTTP server...")

	// Graceful shutdown with timeout
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := httpServer.Stop(shutdownCtx); err != nil {
		log.Printf("HTTP server shutdown error: %v", err)
	} else {
		log.Println("HTTP server stopped gracefully")
	}
}
