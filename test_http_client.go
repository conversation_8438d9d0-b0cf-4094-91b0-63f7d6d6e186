package main

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

type BarcodeGenerateSeedRequest struct {
	EasyID    int64  `json:"easyId"`
	DeviceID  string `json:"deviceId"`
	SessionID string `json:"sessionId"`
	IPAddress string `json:"ipAddress"`
}

type BarcodeGenerateSeedResponse struct {
	KeyID         string `json:"keyId"`
	OtpSeed       string `json:"otpSeed"`
	MinExpiryDate string `json:"minExpiryDate"`
}

func main() {
	// Generate JWT token
	secretKey := "dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU="
	token := generateJWT(secretKey, "rpay", []string{"barcode"})

	// Test health endpoint
	fmt.Println("=== Testing Health Endpoint ===")
	resp, err := http.Get("http://localhost:8081/health")
	if err != nil {
		log.Printf("Error calling health endpoint: %v", err)
	} else {
		body, _ := io.ReadAll(resp.Body)
		fmt.Printf("Health response: %s\n", string(body))
		resp.Body.Close()
	}

	// Test barcode generation endpoint
	fmt.Println("\n=== Testing Barcode Generation Endpoint ===")
	
	// Create request
	req := BarcodeGenerateSeedRequest{
		EasyID:    123456,
		DeviceID:  "device123",
		SessionID: "session123",
		IPAddress: "***********",
	}

	// Convert to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		log.Fatalf("Error marshaling request: %v", err)
	}

	// Create HTTP request
	httpReq, err := http.NewRequest("POST", "http://localhost:8081/api/v1/barcode/generate-seed", bytes.NewBuffer(jsonData))
	if err != nil {
		log.Fatalf("Error creating request: %v", err)
	}

	// Add headers
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+token)

	// Make request
	client := &http.Client{}
	resp, err = client.Do(httpReq)
	if err != nil {
		log.Printf("Error making request: %v", err)
		return
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Printf("Error reading response: %v", err)
		return
	}

	fmt.Printf("Status Code: %d\n", resp.StatusCode)
	fmt.Printf("Response: %s\n", string(body))

	// Parse response if successful
	if resp.StatusCode == 200 {
		var seedResp BarcodeGenerateSeedResponse
		if err := json.Unmarshal(body, &seedResp); err == nil {
			fmt.Printf("\nParsed Response:\n")
			fmt.Printf("  KeyID: %s\n", seedResp.KeyID)
			fmt.Printf("  OtpSeed: %s\n", seedResp.OtpSeed)
			fmt.Printf("  MinExpiryDate: %s\n", seedResp.MinExpiryDate)
		}
	}

	fmt.Println("\n=== Test completed ===")
}

func generateJWT(secretKeyBase64, subject string, groups []string) string {
	// Decode the secret key
	secretKey, err := base64.StdEncoding.DecodeString(secretKeyBase64)
	if err != nil {
		log.Fatalf("Failed to decode secret key: %v", err)
	}

	// Create the claims
	claims := jwt.MapClaims{
		"sub":    subject,
		"groups": groups,
		"exp":    time.Now().Add(time.Hour).Unix(),
		"iat":    time.Now().Unix(),
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign the token
	tokenString, err := token.SignedString(secretKey)
	if err != nil {
		log.Fatalf("Failed to sign token: %v", err)
	}

	return tokenString
}
