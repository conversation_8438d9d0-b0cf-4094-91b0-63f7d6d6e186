// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: proto/shopper_barcode_service.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BarcodeGenerateSeedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EasyId        int64                  `protobuf:"varint,1,opt,name=easyId,proto3" json:"easyId,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	SessionId     string                 `protobuf:"bytes,3,opt,name=sessionId,proto3" json:"sessionId,omitempty"`
	IpAddress     string                 `protobuf:"bytes,4,opt,name=ipAddress,proto3" json:"ipAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BarcodeGenerateSeedRequest) Reset() {
	*x = BarcodeGenerateSeedRequest{}
	mi := &file_proto_shopper_barcode_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BarcodeGenerateSeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarcodeGenerateSeedRequest) ProtoMessage() {}

func (x *BarcodeGenerateSeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shopper_barcode_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarcodeGenerateSeedRequest.ProtoReflect.Descriptor instead.
func (*BarcodeGenerateSeedRequest) Descriptor() ([]byte, []int) {
	return file_proto_shopper_barcode_service_proto_rawDescGZIP(), []int{0}
}

func (x *BarcodeGenerateSeedRequest) GetEasyId() int64 {
	if x != nil {
		return x.EasyId
	}
	return 0
}

func (x *BarcodeGenerateSeedRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *BarcodeGenerateSeedRequest) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

func (x *BarcodeGenerateSeedRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type BarcodeGenerateSeedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	KeyId         string                 `protobuf:"bytes,1,opt,name=keyId,proto3" json:"keyId,omitempty"`
	OtpSeed       string                 `protobuf:"bytes,2,opt,name=otpSeed,proto3" json:"otpSeed,omitempty"`
	MinExpiryDate string                 `protobuf:"bytes,3,opt,name=minExpiryDate,proto3" json:"minExpiryDate,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BarcodeGenerateSeedResponse) Reset() {
	*x = BarcodeGenerateSeedResponse{}
	mi := &file_proto_shopper_barcode_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BarcodeGenerateSeedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarcodeGenerateSeedResponse) ProtoMessage() {}

func (x *BarcodeGenerateSeedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shopper_barcode_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarcodeGenerateSeedResponse.ProtoReflect.Descriptor instead.
func (*BarcodeGenerateSeedResponse) Descriptor() ([]byte, []int) {
	return file_proto_shopper_barcode_service_proto_rawDescGZIP(), []int{1}
}

func (x *BarcodeGenerateSeedResponse) GetKeyId() string {
	if x != nil {
		return x.KeyId
	}
	return ""
}

func (x *BarcodeGenerateSeedResponse) GetOtpSeed() string {
	if x != nil {
		return x.OtpSeed
	}
	return ""
}

func (x *BarcodeGenerateSeedResponse) GetMinExpiryDate() string {
	if x != nil {
		return x.MinExpiryDate
	}
	return ""
}

var File_proto_shopper_barcode_service_proto protoreflect.FileDescriptor

const file_proto_shopper_barcode_service_proto_rawDesc = "" +
	"\n" +
	"#proto/shopper_barcode_service.proto\"\x8c\x01\n" +
	"\x1aBarcodeGenerateSeedRequest\x12\x16\n" +
	"\x06easyId\x18\x01 \x01(\x03R\x06easyId\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tsessionId\x18\x03 \x01(\tR\tsessionId\x12\x1c\n" +
	"\tipAddress\x18\x04 \x01(\tR\tipAddress\"s\n" +
	"\x1bBarcodeGenerateSeedResponse\x12\x14\n" +
	"\x05keyId\x18\x01 \x01(\tR\x05keyId\x12\x18\n" +
	"\aotpSeed\x18\x02 \x01(\tR\aotpSeed\x12$\n" +
	"\rminExpiryDate\x18\x03 \x01(\tR\rminExpiryDate2s\n" +
	"\x15ShopperBarcodeService\x12Z\n" +
	"\x1dGetShopperBarcodeGenerateSeed\x12\x1b.BarcodeGenerateSeedRequest\x1a\x1c.BarcodeGenerateSeedResponseB\x1fZ\x1drpay-barcode-service-go/protob\x06proto3"

var (
	file_proto_shopper_barcode_service_proto_rawDescOnce sync.Once
	file_proto_shopper_barcode_service_proto_rawDescData []byte
)

func file_proto_shopper_barcode_service_proto_rawDescGZIP() []byte {
	file_proto_shopper_barcode_service_proto_rawDescOnce.Do(func() {
		file_proto_shopper_barcode_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_shopper_barcode_service_proto_rawDesc), len(file_proto_shopper_barcode_service_proto_rawDesc)))
	})
	return file_proto_shopper_barcode_service_proto_rawDescData
}

var file_proto_shopper_barcode_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_shopper_barcode_service_proto_goTypes = []any{
	(*BarcodeGenerateSeedRequest)(nil),  // 0: BarcodeGenerateSeedRequest
	(*BarcodeGenerateSeedResponse)(nil), // 1: BarcodeGenerateSeedResponse
}
var file_proto_shopper_barcode_service_proto_depIdxs = []int32{
	0, // 0: ShopperBarcodeService.GetShopperBarcodeGenerateSeed:input_type -> BarcodeGenerateSeedRequest
	1, // 1: ShopperBarcodeService.GetShopperBarcodeGenerateSeed:output_type -> BarcodeGenerateSeedResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_shopper_barcode_service_proto_init() }
func file_proto_shopper_barcode_service_proto_init() {
	if File_proto_shopper_barcode_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_shopper_barcode_service_proto_rawDesc), len(file_proto_shopper_barcode_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_shopper_barcode_service_proto_goTypes,
		DependencyIndexes: file_proto_shopper_barcode_service_proto_depIdxs,
		MessageInfos:      file_proto_shopper_barcode_service_proto_msgTypes,
	}.Build()
	File_proto_shopper_barcode_service_proto = out.File
	file_proto_shopper_barcode_service_proto_goTypes = nil
	file_proto_shopper_barcode_service_proto_depIdxs = nil
}
