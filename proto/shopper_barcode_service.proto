syntax = "proto3";

option go_package = "rpay-barcode-service-go/proto";

message BarcodeGenerateSeedRequest {
    int64 easyId = 1;
    string deviceId = 2;
    string sessionId = 3;
    string ipAddress = 4;
}

message BarcodeGenerateSeedResponse {
    string keyId = 1;
    string otpSeed = 2;
    string minExpiryDate = 3;
}

service ShopperBarcodeService {
  rpc GetShopperBarcodeGenerateSeed(BarcodeGenerateSeedRequest) returns (BarcodeGenerateSeedResponse);
}
