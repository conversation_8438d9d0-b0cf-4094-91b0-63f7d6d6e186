// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: proto/shopper_barcode_internal_service.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ShopperBarcodeInternalService_GetShopperBarcodeVerifyOtp_FullMethodName = "/ShopperBarcodeInternalService/GetShopperBarcodeVerifyOtp"
)

// ShopperBarcodeInternalServiceClient is the client API for ShopperBarcodeInternalService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ShopperBarcodeInternalServiceClient interface {
	GetShopperBarcodeVerifyOtp(ctx context.Context, in *BarcodeVerifyOtpRequest, opts ...grpc.CallOption) (*BarcodeVerifyOtpResponse, error)
}

type shopperBarcodeInternalServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewShopperBarcodeInternalServiceClient(cc grpc.ClientConnInterface) ShopperBarcodeInternalServiceClient {
	return &shopperBarcodeInternalServiceClient{cc}
}

func (c *shopperBarcodeInternalServiceClient) GetShopperBarcodeVerifyOtp(ctx context.Context, in *BarcodeVerifyOtpRequest, opts ...grpc.CallOption) (*BarcodeVerifyOtpResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BarcodeVerifyOtpResponse)
	err := c.cc.Invoke(ctx, ShopperBarcodeInternalService_GetShopperBarcodeVerifyOtp_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShopperBarcodeInternalServiceServer is the server API for ShopperBarcodeInternalService service.
// All implementations must embed UnimplementedShopperBarcodeInternalServiceServer
// for forward compatibility.
type ShopperBarcodeInternalServiceServer interface {
	GetShopperBarcodeVerifyOtp(context.Context, *BarcodeVerifyOtpRequest) (*BarcodeVerifyOtpResponse, error)
	mustEmbedUnimplementedShopperBarcodeInternalServiceServer()
}

// UnimplementedShopperBarcodeInternalServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShopperBarcodeInternalServiceServer struct{}

func (UnimplementedShopperBarcodeInternalServiceServer) GetShopperBarcodeVerifyOtp(context.Context, *BarcodeVerifyOtpRequest) (*BarcodeVerifyOtpResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShopperBarcodeVerifyOtp not implemented")
}
func (UnimplementedShopperBarcodeInternalServiceServer) mustEmbedUnimplementedShopperBarcodeInternalServiceServer() {
}
func (UnimplementedShopperBarcodeInternalServiceServer) testEmbeddedByValue() {}

// UnsafeShopperBarcodeInternalServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShopperBarcodeInternalServiceServer will
// result in compilation errors.
type UnsafeShopperBarcodeInternalServiceServer interface {
	mustEmbedUnimplementedShopperBarcodeInternalServiceServer()
}

func RegisterShopperBarcodeInternalServiceServer(s grpc.ServiceRegistrar, srv ShopperBarcodeInternalServiceServer) {
	// If the following call pancis, it indicates UnimplementedShopperBarcodeInternalServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ShopperBarcodeInternalService_ServiceDesc, srv)
}

func _ShopperBarcodeInternalService_GetShopperBarcodeVerifyOtp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BarcodeVerifyOtpRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopperBarcodeInternalServiceServer).GetShopperBarcodeVerifyOtp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShopperBarcodeInternalService_GetShopperBarcodeVerifyOtp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopperBarcodeInternalServiceServer).GetShopperBarcodeVerifyOtp(ctx, req.(*BarcodeVerifyOtpRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShopperBarcodeInternalService_ServiceDesc is the grpc.ServiceDesc for ShopperBarcodeInternalService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShopperBarcodeInternalService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ShopperBarcodeInternalService",
	HandlerType: (*ShopperBarcodeInternalServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetShopperBarcodeVerifyOtp",
			Handler:    _ShopperBarcodeInternalService_GetShopperBarcodeVerifyOtp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/shopper_barcode_internal_service.proto",
}
