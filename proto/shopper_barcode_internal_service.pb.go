// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: proto/shopper_barcode_internal_service.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BarcodeVerifyOtpRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	OfflineBarcode string                 `protobuf:"bytes,1,opt,name=offlineBarcode,proto3" json:"offlineBarcode,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BarcodeVerifyOtpRequest) Reset() {
	*x = BarcodeVerifyOtpRequest{}
	mi := &file_proto_shopper_barcode_internal_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BarcodeVerifyOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarcodeVerifyOtpRequest) ProtoMessage() {}

func (x *BarcodeVerifyOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shopper_barcode_internal_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarcodeVerifyOtpRequest.ProtoReflect.Descriptor instead.
func (*BarcodeVerifyOtpRequest) Descriptor() ([]byte, []int) {
	return file_proto_shopper_barcode_internal_service_proto_rawDescGZIP(), []int{0}
}

func (x *BarcodeVerifyOtpRequest) GetOfflineBarcode() string {
	if x != nil {
		return x.OfflineBarcode
	}
	return ""
}

type BarcodeVerifyOtpResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        bool                   `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Barcode       *string                `protobuf:"bytes,2,opt,name=barcode,proto3,oneof" json:"barcode,omitempty"`
	BarcodeId     *int64                 `protobuf:"varint,3,opt,name=barcodeId,proto3,oneof" json:"barcodeId,omitempty"`
	EasyId        *int64                 `protobuf:"varint,4,opt,name=easyId,proto3,oneof" json:"easyId,omitempty"`
	DeviceId      *string                `protobuf:"bytes,5,opt,name=deviceId,proto3,oneof" json:"deviceId,omitempty"`
	IpAddress     *string                `protobuf:"bytes,6,opt,name=ipAddress,proto3,oneof" json:"ipAddress,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BarcodeVerifyOtpResponse) Reset() {
	*x = BarcodeVerifyOtpResponse{}
	mi := &file_proto_shopper_barcode_internal_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BarcodeVerifyOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BarcodeVerifyOtpResponse) ProtoMessage() {}

func (x *BarcodeVerifyOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_shopper_barcode_internal_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BarcodeVerifyOtpResponse.ProtoReflect.Descriptor instead.
func (*BarcodeVerifyOtpResponse) Descriptor() ([]byte, []int) {
	return file_proto_shopper_barcode_internal_service_proto_rawDescGZIP(), []int{1}
}

func (x *BarcodeVerifyOtpResponse) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *BarcodeVerifyOtpResponse) GetBarcode() string {
	if x != nil && x.Barcode != nil {
		return *x.Barcode
	}
	return ""
}

func (x *BarcodeVerifyOtpResponse) GetBarcodeId() int64 {
	if x != nil && x.BarcodeId != nil {
		return *x.BarcodeId
	}
	return 0
}

func (x *BarcodeVerifyOtpResponse) GetEasyId() int64 {
	if x != nil && x.EasyId != nil {
		return *x.EasyId
	}
	return 0
}

func (x *BarcodeVerifyOtpResponse) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

func (x *BarcodeVerifyOtpResponse) GetIpAddress() string {
	if x != nil && x.IpAddress != nil {
		return *x.IpAddress
	}
	return ""
}

var File_proto_shopper_barcode_internal_service_proto protoreflect.FileDescriptor

const file_proto_shopper_barcode_internal_service_proto_rawDesc = "" +
	"\n" +
	",proto/shopper_barcode_internal_service.proto\"A\n" +
	"\x17BarcodeVerifyOtpRequest\x12&\n" +
	"\x0eofflineBarcode\x18\x01 \x01(\tR\x0eofflineBarcode\"\x95\x02\n" +
	"\x18BarcodeVerifyOtpResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\bR\x06status\x12\x1d\n" +
	"\abarcode\x18\x02 \x01(\tH\x00R\abarcode\x88\x01\x01\x12!\n" +
	"\tbarcodeId\x18\x03 \x01(\x03H\x01R\tbarcodeId\x88\x01\x01\x12\x1b\n" +
	"\x06easyId\x18\x04 \x01(\x03H\x02R\x06easyId\x88\x01\x01\x12\x1f\n" +
	"\bdeviceId\x18\x05 \x01(\tH\x03R\bdeviceId\x88\x01\x01\x12!\n" +
	"\tipAddress\x18\x06 \x01(\tH\x04R\tipAddress\x88\x01\x01B\n" +
	"\n" +
	"\b_barcodeB\f\n" +
	"\n" +
	"_barcodeIdB\t\n" +
	"\a_easyIdB\v\n" +
	"\t_deviceIdB\f\n" +
	"\n" +
	"_ipAddress2r\n" +
	"\x1dShopperBarcodeInternalService\x12Q\n" +
	"\x1aGetShopperBarcodeVerifyOtp\x12\x18.BarcodeVerifyOtpRequest\x1a\x19.BarcodeVerifyOtpResponseB\x1fZ\x1drpay-barcode-service-go/protob\x06proto3"

var (
	file_proto_shopper_barcode_internal_service_proto_rawDescOnce sync.Once
	file_proto_shopper_barcode_internal_service_proto_rawDescData []byte
)

func file_proto_shopper_barcode_internal_service_proto_rawDescGZIP() []byte {
	file_proto_shopper_barcode_internal_service_proto_rawDescOnce.Do(func() {
		file_proto_shopper_barcode_internal_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_shopper_barcode_internal_service_proto_rawDesc), len(file_proto_shopper_barcode_internal_service_proto_rawDesc)))
	})
	return file_proto_shopper_barcode_internal_service_proto_rawDescData
}

var file_proto_shopper_barcode_internal_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_proto_shopper_barcode_internal_service_proto_goTypes = []any{
	(*BarcodeVerifyOtpRequest)(nil),  // 0: BarcodeVerifyOtpRequest
	(*BarcodeVerifyOtpResponse)(nil), // 1: BarcodeVerifyOtpResponse
}
var file_proto_shopper_barcode_internal_service_proto_depIdxs = []int32{
	0, // 0: ShopperBarcodeInternalService.GetShopperBarcodeVerifyOtp:input_type -> BarcodeVerifyOtpRequest
	1, // 1: ShopperBarcodeInternalService.GetShopperBarcodeVerifyOtp:output_type -> BarcodeVerifyOtpResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_shopper_barcode_internal_service_proto_init() }
func file_proto_shopper_barcode_internal_service_proto_init() {
	if File_proto_shopper_barcode_internal_service_proto != nil {
		return
	}
	file_proto_shopper_barcode_internal_service_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_shopper_barcode_internal_service_proto_rawDesc), len(file_proto_shopper_barcode_internal_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_shopper_barcode_internal_service_proto_goTypes,
		DependencyIndexes: file_proto_shopper_barcode_internal_service_proto_depIdxs,
		MessageInfos:      file_proto_shopper_barcode_internal_service_proto_msgTypes,
	}.Build()
	File_proto_shopper_barcode_internal_service_proto = out.File
	file_proto_shopper_barcode_internal_service_proto_goTypes = nil
	file_proto_shopper_barcode_internal_service_proto_depIdxs = nil
}
