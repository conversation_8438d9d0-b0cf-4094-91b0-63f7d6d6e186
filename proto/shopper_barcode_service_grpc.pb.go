// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.32.1
// source: proto/shopper_barcode_service.proto

package proto

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ShopperBarcodeService_GetShopperBarcodeGenerateSeed_FullMethodName = "/ShopperBarcodeService/GetShopperBarcodeGenerateSeed"
)

// ShopperBarcodeServiceClient is the client API for ShopperBarcodeService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ShopperBarcodeServiceClient interface {
	GetShopperBarcodeGenerateSeed(ctx context.Context, in *BarcodeGenerateSeedRequest, opts ...grpc.CallOption) (*BarcodeGenerateSeedResponse, error)
}

type shopperBarcodeServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewShopperBarcodeServiceClient(cc grpc.ClientConnInterface) ShopperBarcodeServiceClient {
	return &shopperBarcodeServiceClient{cc}
}

func (c *shopperBarcodeServiceClient) GetShopperBarcodeGenerateSeed(ctx context.Context, in *BarcodeGenerateSeedRequest, opts ...grpc.CallOption) (*BarcodeGenerateSeedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BarcodeGenerateSeedResponse)
	err := c.cc.Invoke(ctx, ShopperBarcodeService_GetShopperBarcodeGenerateSeed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ShopperBarcodeServiceServer is the server API for ShopperBarcodeService service.
// All implementations must embed UnimplementedShopperBarcodeServiceServer
// for forward compatibility.
type ShopperBarcodeServiceServer interface {
	GetShopperBarcodeGenerateSeed(context.Context, *BarcodeGenerateSeedRequest) (*BarcodeGenerateSeedResponse, error)
	mustEmbedUnimplementedShopperBarcodeServiceServer()
}

// UnimplementedShopperBarcodeServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedShopperBarcodeServiceServer struct{}

func (UnimplementedShopperBarcodeServiceServer) GetShopperBarcodeGenerateSeed(context.Context, *BarcodeGenerateSeedRequest) (*BarcodeGenerateSeedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetShopperBarcodeGenerateSeed not implemented")
}
func (UnimplementedShopperBarcodeServiceServer) mustEmbedUnimplementedShopperBarcodeServiceServer() {}
func (UnimplementedShopperBarcodeServiceServer) testEmbeddedByValue()                               {}

// UnsafeShopperBarcodeServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ShopperBarcodeServiceServer will
// result in compilation errors.
type UnsafeShopperBarcodeServiceServer interface {
	mustEmbedUnimplementedShopperBarcodeServiceServer()
}

func RegisterShopperBarcodeServiceServer(s grpc.ServiceRegistrar, srv ShopperBarcodeServiceServer) {
	// If the following call pancis, it indicates UnimplementedShopperBarcodeServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ShopperBarcodeService_ServiceDesc, srv)
}

func _ShopperBarcodeService_GetShopperBarcodeGenerateSeed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BarcodeGenerateSeedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ShopperBarcodeServiceServer).GetShopperBarcodeGenerateSeed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ShopperBarcodeService_GetShopperBarcodeGenerateSeed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ShopperBarcodeServiceServer).GetShopperBarcodeGenerateSeed(ctx, req.(*BarcodeGenerateSeedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ShopperBarcodeService_ServiceDesc is the grpc.ServiceDesc for ShopperBarcodeService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ShopperBarcodeService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "ShopperBarcodeService",
	HandlerType: (*ShopperBarcodeServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetShopperBarcodeGenerateSeed",
			Handler:    _ShopperBarcodeService_GetShopperBarcodeGenerateSeed_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/shopper_barcode_service.proto",
}
