// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        v6.32.1
// source: proto/error_response.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ErrorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ErrorCode     string                 `protobuf:"bytes,1,opt,name=errorCode,proto3" json:"errorCode,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=errorMessage,proto3" json:"errorMessage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorResponse) Reset() {
	*x = ErrorResponse{}
	mi := &file_proto_error_response_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorResponse) ProtoMessage() {}

func (x *ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_error_response_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorResponse.ProtoReflect.Descriptor instead.
func (*ErrorResponse) Descriptor() ([]byte, []int) {
	return file_proto_error_response_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorResponse) GetErrorCode() string {
	if x != nil {
		return x.ErrorCode
	}
	return ""
}

func (x *ErrorResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

var File_proto_error_response_proto protoreflect.FileDescriptor

const file_proto_error_response_proto_rawDesc = "" +
	"\n" +
	"\x1aproto/error_response.proto\"Q\n" +
	"\rErrorResponse\x12\x1c\n" +
	"\terrorCode\x18\x01 \x01(\tR\terrorCode\x12\"\n" +
	"\ferrorMessage\x18\x02 \x01(\tR\ferrorMessageB\x1fZ\x1drpay-barcode-service-go/protob\x06proto3"

var (
	file_proto_error_response_proto_rawDescOnce sync.Once
	file_proto_error_response_proto_rawDescData []byte
)

func file_proto_error_response_proto_rawDescGZIP() []byte {
	file_proto_error_response_proto_rawDescOnce.Do(func() {
		file_proto_error_response_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_error_response_proto_rawDesc), len(file_proto_error_response_proto_rawDesc)))
	})
	return file_proto_error_response_proto_rawDescData
}

var file_proto_error_response_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_error_response_proto_goTypes = []any{
	(*ErrorResponse)(nil), // 0: ErrorResponse
}
var file_proto_error_response_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_error_response_proto_init() }
func file_proto_error_response_proto_init() {
	if File_proto_error_response_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_error_response_proto_rawDesc), len(file_proto_error_response_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_error_response_proto_goTypes,
		DependencyIndexes: file_proto_error_response_proto_depIdxs,
		MessageInfos:      file_proto_error_response_proto_msgTypes,
	}.Build()
	File_proto_error_response_proto = out.File
	file_proto_error_response_proto_goTypes = nil
	file_proto_error_response_proto_depIdxs = nil
}
