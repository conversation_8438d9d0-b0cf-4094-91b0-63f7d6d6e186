package model

import "time"

// BarcodeSeedDetails represents the barcode seed details entity
type BarcodeSeedDetails struct {
	BarcodeSeedDetailsID int64     `json:"barcodeSeedDetailsId"`
	EasyID               int64     `json:"easyId"`
	DeviceID             string    `json:"deviceId"`
	IPAddress            string    `json:"ipAddress"`
	KeyID                string    `json:"keyId"`
	OtpSeed              string    `json:"otpSeed"`
	DeletedFlag          string    `json:"deletedFlag"`
	CreateDateTime       time.Time `json:"createDateTime"`
	UpdateDateTime       time.Time `json:"updateDateTime"`
	MinExpiryDateTime    time.Time `json:"minExpiryDateTime"`
	MaxExpiryDateTime    time.Time `json:"maxExpiryDateTime"`
}

// BarcodeForShopper represents the barcode for shopper entity
type BarcodeForShopper struct {
	BarcodeForShopperID int64     `json:"barcodeForShopperId"`
	Barcode             string    `json:"barcode"`
	EasyID              int64     `json:"easyId"`
	PointUsage          string    `json:"pointUsage"`
	PointPaymentAmount  int64     `json:"pointPaymentAmount"`
	IPAddress           string    `json:"ipAddress"`
	MailHash            string    `json:"mailHash"`
	DeviceID            string    `json:"deviceId"`
	Latitude            *float64  `json:"latitude"`
	Longitude           *float64  `json:"longitude"`
	ExpireDatetime      time.Time `json:"expireDatetime"`
	Scanned             string    `json:"scanned"`
	DeletedFlag         string    `json:"deletedFlag"`
	CreateDatetime      time.Time `json:"createDatetime"`
	UpdateDatetime      time.Time `json:"updateDatetime"`
	Operator            string    `json:"operator"`
	Timestamp           time.Time `json:"timestamp"`
	PointPriority       *string   `json:"pointPriority"`
}

// OfflineBarcodeTransaction represents the offline barcode transaction entity
type OfflineBarcodeTransaction struct {
	OfflineBarcodeID int64     `json:"offlineBarcodeId"`
	OfflineBarcode   string    `json:"offlineBarcode"`
	BarcodeID        int64     `json:"barcodeId"`
	PointUsage       string    `json:"pointUsage"`
	PointPriority    *string   `json:"pointPriority"`
	PointAmount      int64     `json:"pointAmount"`
	DeviceID         string    `json:"deviceId"`
	EasyID           int64     `json:"easyId"`
	KeyID            string    `json:"keyId"`
	DeletedFlag      string    `json:"deletedFlag"`
	CreatedDate      time.Time `json:"createdDate"`
}

// BarcodeSplit represents the split components of an offline barcode
type BarcodeSplit struct {
	JpqrCodePrefix    string `json:"jpqrCodePrefix"`
	JpqrRpayPrefix    string `json:"jpqrRpayPrefix"`
	BarcodeIdentifier string `json:"barcodeIdentifier"`
	KeyID             string `json:"keyId"`
	Totp              string `json:"totp"`
}

// Constants for enums
const (
	// Point Usage
	PointUsageUsePartOff = "0"
	
	// Scanned status
	ScannedNotScanned = "0"
	ScannedScanned    = "1"
	
	// Deleted flag
	DeletedFlagNotDeleted = "0"
	DeletedFlagDeleted    = "1"
	
	// Operator
	OperatorWeb = "0"
	
	// API Version
	APIVersionV20 = "2.0"
	
	// Barcode prefixes
	JPQROfflineCodePrefix = "8000"
	JPQRRpayIDPrefix      = "01"
)
