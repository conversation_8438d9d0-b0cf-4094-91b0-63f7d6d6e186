package database

import (
	"context"
	"log"
	"time"

	"rpay-barcode-service-go/pkg/cache"
	"rpay-barcode-service-go/pkg/model"
)

// DatabaseInterface defines the database operations
type DatabaseInterface interface {
	FindBarcodeSeedDetailsByKeyID(keyID string) (*model.BarcodeSeedDetails, error)
	FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID int64, deviceID string) (*model.BarcodeSeedDetails, error)
	CreateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error
	UpdateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error
	CreateBarcodeForShopper(bfs *model.BarcodeForShopper) (int64, error)
	CreateOfflineBarcodeTransaction(obt *model.OfflineBarcodeTransaction) (int64, error)
}

// CachedDB wraps a database with Redis caching
type CachedDB struct {
	db    DatabaseInterface
	cache cache.CacheInterface
}

// NewCachedDB creates a new cached database instance
func NewCachedDB(db DatabaseInterface, cache cache.CacheInterface) *CachedDB {
	return &CachedDB{
		db:    db,
		cache: cache,
	}
}

// FindBarcodeSeedDetailsByKeyID finds barcode seed details by keyID with caching
func (c *CachedDB) FindBarcodeSeedDetailsByKeyID(keyID string) (*model.BarcodeSeedDetails, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Try cache first
	if details, err := c.cache.GetBarcodeSeedDetailsByKeyID(ctx, keyID); err == nil && details != nil {
		return details, nil
	}

	// Cache miss or error, query database
	log.Printf("Cache MISS: Querying database for keyID: %s", keyID)
	details, err := c.db.FindBarcodeSeedDetailsByKeyID(keyID)
	if err != nil {
		return nil, err
	}

	// If found in database, cache it
	if details != nil {
		go func() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			if err := c.cache.SetBarcodeSeedDetails(ctx, details); err != nil {
				log.Printf("Failed to cache barcode seed details: %v", err)
			}
		}()
	}

	return details, nil
}

// FindBarcodeSeedDetailsByEasyIDAndDeviceID finds barcode seed details by easyID and deviceID with caching
func (c *CachedDB) FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Try cache first
	if details, err := c.cache.GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx, easyID, deviceID); err == nil && details != nil {
		return details, nil
	}

	// Cache miss or error, query database
	log.Printf("Cache MISS: Querying database for easyID: %d, deviceID: %s", easyID, deviceID)
	details, err := c.db.FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID, deviceID)
	if err != nil {
		return nil, err
	}

	// If found in database, cache it
	if details != nil {
		go func() {
			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()
			if err := c.cache.SetBarcodeSeedDetails(ctx, details); err != nil {
				log.Printf("Failed to cache barcode seed details: %v", err)
			}
		}()
	}

	return details, nil
}

// CreateBarcodeSeedDetails creates barcode seed details and updates cache
func (c *CachedDB) CreateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	// Create in database first
	if err := c.db.CreateBarcodeSeedDetails(details); err != nil {
		return err
	}

	// Update cache asynchronously
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := c.cache.SetBarcodeSeedDetails(ctx, details); err != nil {
			log.Printf("Failed to cache new barcode seed details: %v", err)
		}
	}()

	return nil
}

// UpdateBarcodeSeedDetails updates barcode seed details and invalidates cache
func (c *CachedDB) UpdateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	// Update in database first
	if err := c.db.UpdateBarcodeSeedDetails(details); err != nil {
		return err
	}

	// Update cache asynchronously
	go func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Delete old cache entries first (in case keyID or user info changed)
		if err := c.cache.DeleteBarcodeSeedDetails(ctx, details.KeyID, details.EasyID, details.DeviceID); err != nil {
			log.Printf("Failed to delete old cache entries: %v", err)
		}

		// Set new cache entries
		if err := c.cache.SetBarcodeSeedDetails(ctx, details); err != nil {
			log.Printf("Failed to cache updated barcode seed details: %v", err)
		}
	}()

	return nil
}

// CreateBarcodeForShopper creates barcode for shopper (no caching needed for this table)
func (c *CachedDB) CreateBarcodeForShopper(bfs *model.BarcodeForShopper) (int64, error) {
	return c.db.CreateBarcodeForShopper(bfs)
}

// CreateOfflineBarcodeTransaction creates offline barcode transaction (no caching needed for this table)
func (c *CachedDB) CreateOfflineBarcodeTransaction(obt *model.OfflineBarcodeTransaction) (int64, error) {
	return c.db.CreateOfflineBarcodeTransaction(obt)
}

// Close closes the cache connection
func (c *CachedDB) Close() error {
	return c.cache.Close()
}
