package database

import (
	"testing"
	"time"

	"rpay-barcode-service-go/pkg/model"
)

// Note: These tests require a running MySQL database with the proper schema
// They are integration tests and will be skipped if the database is not available

func TestNewMySQLDB_InvalidDSN(t *testing.T) {
	// Test with invalid DSN
	_, err := NewMySQLDB("invalid-dsn")
	if err == nil {
		t.Error("Expected error for invalid DSN, got nil")
	}
}

func TestNewMySQLDB_ValidDSN(t *testing.T) {
	// This test requires a running MySQL database
	// Skip if DATABASE_DSN environment variable is not set
	dsn := "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"

	db, err := NewMySQLDB(dsn)
	if err != nil {
		t.Skipf("Skipping MySQL test - database not available: %v", err)
		return
	}
	defer db.Close()

	if db == nil {
		t.Error("NewMySQLDB() returned nil database")
	}
}

func TestMySQLDB_BarcodeSeedDetails_CRUD(t *testing.T) {
	// This test requires a running MySQL database
	dsn := "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"

	db, err := NewMySQLDB(dsn)
	if err != nil {
		t.Skipf("Skipping MySQL test - database not available: %v", err)
		return
	}
	defer db.Close()

	now := time.Now()

	// Test data
	details := &model.BarcodeSeedDetails{
		DeviceID:          "test-device-123",
		EasyID:            999999,
		IPAddress:         "*************",
		KeyID:             "12345678901",
		OtpSeed:           "TESTSEEDFORUNIT",
		DeletedFlag:       "0",
		CreateDateTime:    now,
		UpdateDateTime:    now,
		MinExpiryDateTime: now.Add(24 * time.Hour),
		MaxExpiryDateTime: now.Add(48 * time.Hour),
	}

	// Test Create
	err = db.CreateBarcodeSeedDetails(details)
	if err != nil {
		t.Fatalf("CreateBarcodeSeedDetails() error = %v", err)
	}

	if details.BarcodeSeedDetailsID == 0 {
		t.Error("BarcodeSeedDetailsID should be set after creation")
	}

	// Test FindByKeyID
	found, err := db.FindBarcodeSeedDetailsByKeyID(details.KeyID)
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByKeyID() error = %v", err)
	}

	if found == nil {
		t.Fatal("FindBarcodeSeedDetailsByKeyID() returned nil")
	}

	if found.KeyID != details.KeyID {
		t.Errorf("KeyID = %s, want %s", found.KeyID, details.KeyID)
	}

	if found.EasyID != details.EasyID {
		t.Errorf("EasyID = %d, want %d", found.EasyID, details.EasyID)
	}

	// Test FindByEasyIDAndDeviceID
	found2, err := db.FindBarcodeSeedDetailsByEasyIDAndDeviceID(details.EasyID, details.DeviceID)
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByEasyIDAndDeviceID() error = %v", err)
	}

	if found2 == nil {
		t.Fatal("FindBarcodeSeedDetailsByEasyIDAndDeviceID() returned nil")
	}

	if found2.KeyID != details.KeyID {
		t.Errorf("KeyID = %s, want %s", found2.KeyID, details.KeyID)
	}

	// Test Update
	details.OtpSeed = "UPDATEDSEEDTEST"
	details.UpdateDateTime = time.Now()

	err = db.UpdateBarcodeSeedDetails(details)
	if err != nil {
		t.Fatalf("UpdateBarcodeSeedDetails() error = %v", err)
	}

	// Verify update
	updated, err := db.FindBarcodeSeedDetailsByKeyID(details.KeyID)
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByKeyID() after update error = %v", err)
	}

	if updated.OtpSeed != "UPDATEDSEEDTEST" {
		t.Errorf("OtpSeed after update = %s, want UPDATEDSEEDTEST", updated.OtpSeed)
	}

	// Cleanup - mark as deleted (soft delete)
	_, err = db.db.Exec("UPDATE BARCODE_SEED_DETAILS SET DELETED_FLAG = '1' WHERE KEY_ID = ?", details.KeyID)
	if err != nil {
		t.Logf("Cleanup failed: %v", err)
	}
}

func TestMySQLDB_FindNonExistent(t *testing.T) {
	// This test requires a running MySQL database
	dsn := "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"

	db, err := NewMySQLDB(dsn)
	if err != nil {
		t.Skipf("Skipping MySQL test - database not available: %v", err)
		return
	}
	defer db.Close()

	// Test finding non-existent record by KeyID
	result, err := db.FindBarcodeSeedDetailsByKeyID("nonexistent")
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByKeyID() error = %v", err)
	}

	if result != nil {
		t.Error("FindBarcodeSeedDetailsByKeyID() should return nil for non-existent record")
	}

	// Test finding non-existent record by EasyID and DeviceID
	result, err = db.FindBarcodeSeedDetailsByEasyIDAndDeviceID(999999999, "nonexistent")
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByEasyIDAndDeviceID() error = %v", err)
	}

	if result != nil {
		t.Error("FindBarcodeSeedDetailsByEasyIDAndDeviceID() should return nil for non-existent record")
	}
}

func TestMySQLDB_BarcodeForShopper(t *testing.T) {
	// This test requires a running MySQL database
	dsn := "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"

	db, err := NewMySQLDB(dsn)
	if err != nil {
		t.Skipf("Skipping MySQL test - database not available: %v", err)
		return
	}
	defer db.Close()

	now := time.Now()

	bfs := &model.BarcodeForShopper{
		Barcode:            "1234567890123456",
		EasyID:             999999,
		PointUsage:         "1",
		PointPaymentAmount: 1000,
		IPAddress:          "*************",
		MailHash:           "testhash1234567890123456789012345678901234567890",
		DeviceID:           "test-device-123",
		Latitude:           func() *float64 { v := 35.6762; return &v }(),
		Longitude:          func() *float64 { v := 139.6503; return &v }(),
		ExpireDatetime:     now.Add(24 * time.Hour),
		Scanned:            "0",
		DeletedFlag:        "0",
		CreateDatetime:     now,
		UpdateDatetime:     now,
		Operator:           "test",
		Timestamp:          now,
	}

	id, err := db.CreateBarcodeForShopper(bfs)
	if err != nil {
		t.Fatalf("CreateBarcodeForShopper() error = %v", err)
	}

	if id == 0 {
		t.Error("CreateBarcodeForShopper() should return non-zero ID")
	}

	// Cleanup
	_, err = db.db.Exec("DELETE FROM BARCODE_FOR_SHOPPER WHERE BARCODE_FOR_SHOPPER_ID = ?", id)
	if err != nil {
		t.Logf("Cleanup failed: %v", err)
	}
}

func TestMySQLDB_OfflineBarcodeTransaction(t *testing.T) {
	// This test requires a running MySQL database
	dsn := "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"

	db, err := NewMySQLDB(dsn)
	if err != nil {
		t.Skipf("Skipping MySQL test - database not available: %v", err)
		return
	}
	defer db.Close()

	now := time.Now()

	obt := &model.OfflineBarcodeTransaction{
		OfflineBarcode: "1234567890123456",
		BarcodeID:      1,
		PointUsage:     "1",
		PointPriority:  func() *string { v := "1"; return &v }(),
		PointAmount:    1000,
		DeviceID:       "test-device-123",
		EasyID:         999999,
		KeyID:          "12345678901",
		DeletedFlag:    "0",
		CreatedDate:    now,
	}

	id, err := db.CreateOfflineBarcodeTransaction(obt)
	if err != nil {
		t.Fatalf("CreateOfflineBarcodeTransaction() error = %v", err)
	}

	if id == 0 {
		t.Error("CreateOfflineBarcodeTransaction() should return non-zero ID")
	}

	// Cleanup
	_, err = db.db.Exec("DELETE FROM OFFLINE_BARCODE_TRANSACTION WHERE OFFLINE_BARCODE_ID = ?", id)
	if err != nil {
		t.Logf("Cleanup failed: %v", err)
	}
}
