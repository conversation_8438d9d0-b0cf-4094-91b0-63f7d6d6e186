package database

import (
	"database/sql"
	"fmt"
	"time"

	_ "github.com/go-sql-driver/mysql"

	"rpay-barcode-service-go/pkg/model"
)

// MySQLDB implements DatabaseInterface using MySQL
type MySQLDB struct {
	db *sql.DB
}

// NewMySQLDB creates a new MySQL database connection
func NewMySQLDB(dsn string) (*MySQLDB, error) {
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(20)
	db.SetMaxIdleConns(10)
	db.SetConnMaxLifetime(5 * time.Minute)

	return &MySQLDB{db: db}, nil
}

// Close closes the database connection
func (m *MySQLDB) Close() error {
	return m.db.Close()
}

// FindBarcodeSeedDetailsByKeyID finds barcode seed details by key ID
func (m *MySQLDB) FindBarcodeSeedDetailsByKeyID(keyID string) (*model.BarcodeSeedDetails, error) {
	query := `
		SELECT BARCODE_SEED_DETAILS_ID, DEVICE_ID, EASY_ID, IP_ADDRESS, KEY_ID, OTP_SEED, 
		       DELETED_FLAG, CREATED_DATETIME, UPDATED_DATETIME, MINIMUM_EXPIRY_DATETIME, MAXIMUM_EXPIRY_DATETIME
		FROM BARCODE_SEED_DETAILS
		WHERE KEY_ID = ? AND DELETED_FLAG = '0'
	`

	row := m.db.QueryRow(query, keyID)

	var details model.BarcodeSeedDetails
	err := row.Scan(
		&details.BarcodeSeedDetailsID,
		&details.DeviceID,
		&details.EasyID,
		&details.IPAddress,
		&details.KeyID,
		&details.OtpSeed,
		&details.DeletedFlag,
		&details.CreateDateTime,
		&details.UpdateDateTime,
		&details.MinExpiryDateTime,
		&details.MaxExpiryDateTime,
	)

	if err == sql.ErrNoRows {
		return nil, nil // Not found
	}
	if err != nil {
		return nil, fmt.Errorf("failed to scan barcode seed details: %w", err)
	}

	return &details, nil
}

// FindBarcodeSeedDetailsByEasyIDAndDeviceID finds barcode seed details by easy ID and device ID
func (m *MySQLDB) FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	query := `
		SELECT BARCODE_SEED_DETAILS_ID, DEVICE_ID, EASY_ID, IP_ADDRESS, KEY_ID, OTP_SEED, 
		       DELETED_FLAG, CREATED_DATETIME, UPDATED_DATETIME, MINIMUM_EXPIRY_DATETIME, MAXIMUM_EXPIRY_DATETIME
		FROM BARCODE_SEED_DETAILS
		WHERE EASY_ID = ? AND DEVICE_ID = ? AND DELETED_FLAG = '0'
	`

	row := m.db.QueryRow(query, easyID, deviceID)

	var details model.BarcodeSeedDetails
	err := row.Scan(
		&details.BarcodeSeedDetailsID,
		&details.DeviceID,
		&details.EasyID,
		&details.IPAddress,
		&details.KeyID,
		&details.OtpSeed,
		&details.DeletedFlag,
		&details.CreateDateTime,
		&details.UpdateDateTime,
		&details.MinExpiryDateTime,
		&details.MaxExpiryDateTime,
	)

	if err == sql.ErrNoRows {
		return nil, nil // Not found
	}
	if err != nil {
		return nil, fmt.Errorf("failed to scan barcode seed details: %w", err)
	}

	return &details, nil
}

// CreateBarcodeSeedDetails creates new barcode seed details
func (m *MySQLDB) CreateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	query := `
		INSERT INTO BARCODE_SEED_DETAILS 
		(DEVICE_ID, EASY_ID, IP_ADDRESS, KEY_ID, OTP_SEED, CREATED_DATETIME, UPDATED_DATETIME, MINIMUM_EXPIRY_DATETIME, MAXIMUM_EXPIRY_DATETIME)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := m.db.Exec(query,
		details.DeviceID,
		details.EasyID,
		details.IPAddress,
		details.KeyID,
		details.OtpSeed,
		details.CreateDateTime,
		details.UpdateDateTime,
		details.MinExpiryDateTime,
		details.MaxExpiryDateTime,
	)

	if err != nil {
		return fmt.Errorf("failed to create barcode seed details: %w", err)
	}

	// Get the auto-generated ID
	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	details.BarcodeSeedDetailsID = id
	return nil
}

// UpdateBarcodeSeedDetails updates existing barcode seed details
func (m *MySQLDB) UpdateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	query := `
		UPDATE BARCODE_SEED_DETAILS
		SET KEY_ID = ?, OTP_SEED = ?, UPDATED_DATETIME = ?, MINIMUM_EXPIRY_DATETIME = ?, MAXIMUM_EXPIRY_DATETIME = ?
		WHERE EASY_ID = ? AND DEVICE_ID = ?
	`

	_, err := m.db.Exec(query,
		details.KeyID,
		details.OtpSeed,
		details.UpdateDateTime,
		details.MinExpiryDateTime,
		details.MaxExpiryDateTime,
		details.EasyID,
		details.DeviceID,
	)

	if err != nil {
		return fmt.Errorf("failed to update barcode seed details: %w", err)
	}

	return nil
}

// CreateBarcodeForShopper creates a new barcode for shopper record
func (m *MySQLDB) CreateBarcodeForShopper(bfs *model.BarcodeForShopper) (int64, error) {
	query := `
		INSERT INTO BARCODE_FOR_SHOPPER 
		(BARCODE, EASY_ID, POINT_USAGE, POINT_PAYMENT_AMOUNT, IP_ADDRESS, MAIL_HASH, DEVICE_ID, 
		 LATITUDE, LONGITUDE, EXPIRE_DATETIME, SCANNED, DELETED_FLAG, CREATE_DATETIME, UPDATE_DATETIME, OPERATOR, TIME_STAMP)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := m.db.Exec(query,
		bfs.Barcode,
		bfs.EasyID,
		bfs.PointUsage,
		bfs.PointPaymentAmount,
		bfs.IPAddress,
		bfs.MailHash,
		bfs.DeviceID,
		bfs.Latitude,
		bfs.Longitude,
		bfs.ExpireDatetime,
		bfs.Scanned,
		bfs.DeletedFlag,
		bfs.CreateDatetime,
		bfs.UpdateDatetime,
		bfs.Operator,
		bfs.Timestamp,
	)

	if err != nil {
		return 0, fmt.Errorf("failed to create barcode for shopper: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return id, nil
}

// CreateOfflineBarcodeTransaction creates a new offline barcode transaction record
func (m *MySQLDB) CreateOfflineBarcodeTransaction(obt *model.OfflineBarcodeTransaction) (int64, error) {
	query := `
		INSERT INTO OFFLINE_BARCODE_TRANSACTION 
		(OFFLINE_BARCODE, BARCODE_ID, POINT_USAGE, POINT_PRIORITY, POINT_AMOUNT, DEVICE_ID, EASY_ID, KEY_ID, DELETED_FLAG, CREATED_DATE)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := m.db.Exec(query,
		obt.OfflineBarcode,
		obt.BarcodeID,
		obt.PointUsage,
		obt.PointPriority,
		obt.PointAmount,
		obt.DeviceID,
		obt.EasyID,
		obt.KeyID,
		obt.DeletedFlag,
		obt.CreatedDate,
	)

	if err != nil {
		return 0, fmt.Errorf("failed to create offline barcode transaction: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, fmt.Errorf("failed to get last insert ID: %w", err)
	}

	return id, nil
}
