package database

import (
	"errors"
	"testing"

	"rpay-barcode-service-go/pkg/cache"
	"rpay-barcode-service-go/pkg/model"
)

// MockDatabase implements DatabaseInterface for testing
type MockDatabase struct {
	barcodeSeedDetails map[string]*model.BarcodeSeedDetails
	barcodeSeedByUser  map[string]*model.BarcodeSeedDetails
	shouldError        bool
}

func NewMockDatabase() *MockDatabase {
	return &MockDatabase{
		barcodeSeedDetails: make(map[string]*model.BarcodeSeedDetails),
		barcodeSeedByUser:  make(map[string]*model.BarcodeSeedDetails),
		shouldError:        false,
	}
}

func (m *MockDatabase) FindBarcodeSeedDetailsByKeyID(keyID string) (*model.BarcodeSeedDetails, error) {
	if m.shouldError {
		return nil, errors.New("mock database error")
	}
	return m.barcodeSeedDetails[keyID], nil
}

func (m *MockDatabase) FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	if m.shouldError {
		return nil, errors.New("mock database error")
	}
	key := string(rune(easyID)) + "_" + deviceID
	return m.barcodeSeedByUser[key], nil
}

func (m *MockDatabase) CreateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	if m.shouldError {
		return errors.New("mock database error")
	}
	m.barcodeSeedDetails[details.KeyID] = details
	key := string(rune(details.EasyID)) + "_" + details.DeviceID
	m.barcodeSeedByUser[key] = details
	return nil
}

func (m *MockDatabase) UpdateBarcodeSeedDetails(details *model.BarcodeSeedDetails) error {
	if m.shouldError {
		return errors.New("mock database error")
	}
	m.barcodeSeedDetails[details.KeyID] = details
	key := string(rune(details.EasyID)) + "_" + details.DeviceID
	m.barcodeSeedByUser[key] = details
	return nil
}

func (m *MockDatabase) CreateBarcodeForShopper(bfs *model.BarcodeForShopper) (int64, error) {
	if m.shouldError {
		return 0, errors.New("mock database error")
	}
	return 1, nil
}

func (m *MockDatabase) CreateOfflineBarcodeTransaction(obt *model.OfflineBarcodeTransaction) (int64, error) {
	if m.shouldError {
		return 0, errors.New("mock database error")
	}
	return 1, nil
}

func TestNewCachedDB(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()

	cachedDB := NewCachedDB(mockDB, mockCache)

	if cachedDB == nil {
		t.Error("NewCachedDB() returned nil")
	}

	if cachedDB.db != mockDB {
		t.Error("CachedDB.db not set correctly")
	}

	if cachedDB.cache != mockCache {
		t.Error("CachedDB.cache not set correctly")
	}
}

func TestCachedDB_FindBarcodeSeedDetailsByKeyID_CacheMiss(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	// Add data to mock database
	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}
	mockDB.barcodeSeedDetails["test123"] = details

	// Should find in database (cache miss)
	result, err := cachedDB.FindBarcodeSeedDetailsByKeyID("test123")
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByKeyID() error = %v", err)
	}

	if result == nil {
		t.Fatal("FindBarcodeSeedDetailsByKeyID() returned nil")
	}

	if result.KeyID != "test123" {
		t.Errorf("KeyID = %s, want test123", result.KeyID)
	}
}

func TestCachedDB_FindBarcodeSeedDetailsByKeyID_NotFound(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	// Should return nil when not found
	result, err := cachedDB.FindBarcodeSeedDetailsByKeyID("nonexistent")
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByKeyID() error = %v", err)
	}

	if result != nil {
		t.Error("FindBarcodeSeedDetailsByKeyID() should return nil for nonexistent key")
	}
}

func TestCachedDB_FindBarcodeSeedDetailsByEasyIDAndDeviceID(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	// Add data to mock database
	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}
	key := string(rune(123456)) + "_device123"
	mockDB.barcodeSeedByUser[key] = details

	// Should find in database
	result, err := cachedDB.FindBarcodeSeedDetailsByEasyIDAndDeviceID(123456, "device123")
	if err != nil {
		t.Fatalf("FindBarcodeSeedDetailsByEasyIDAndDeviceID() error = %v", err)
	}

	if result == nil {
		t.Fatal("FindBarcodeSeedDetailsByEasyIDAndDeviceID() returned nil")
	}

	if result.EasyID != 123456 {
		t.Errorf("EasyID = %d, want 123456", result.EasyID)
	}

	if result.DeviceID != "device123" {
		t.Errorf("DeviceID = %s, want device123", result.DeviceID)
	}
}

func TestCachedDB_CreateBarcodeSeedDetails(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}

	err := cachedDB.CreateBarcodeSeedDetails(details)
	if err != nil {
		t.Fatalf("CreateBarcodeSeedDetails() error = %v", err)
	}

	// Verify it was stored in mock database
	stored := mockDB.barcodeSeedDetails["test123"]
	if stored == nil {
		t.Error("Details not stored in database")
	}

	if stored.KeyID != "test123" {
		t.Errorf("Stored KeyID = %s, want test123", stored.KeyID)
	}
}

func TestCachedDB_UpdateBarcodeSeedDetails(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "UPDATEDSEED",
	}

	err := cachedDB.UpdateBarcodeSeedDetails(details)
	if err != nil {
		t.Fatalf("UpdateBarcodeSeedDetails() error = %v", err)
	}

	// Verify it was updated in mock database
	stored := mockDB.barcodeSeedDetails["test123"]
	if stored == nil {
		t.Error("Details not stored in database")
	}

	if stored.OtpSeed != "UPDATEDSEED" {
		t.Errorf("Stored OtpSeed = %s, want UPDATEDSEED", stored.OtpSeed)
	}
}

func TestCachedDB_DatabaseError(t *testing.T) {
	mockDB := NewMockDatabase()
	mockDB.shouldError = true
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	// Test that database errors are propagated
	_, err := cachedDB.FindBarcodeSeedDetailsByKeyID("test123")
	if err == nil {
		t.Error("Expected error from database, got nil")
	}

	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}

	err = cachedDB.CreateBarcodeSeedDetails(details)
	if err == nil {
		t.Error("Expected error from database, got nil")
	}

	err = cachedDB.UpdateBarcodeSeedDetails(details)
	if err == nil {
		t.Error("Expected error from database, got nil")
	}
}

func TestCachedDB_PassthroughMethods(t *testing.T) {
	mockDB := NewMockDatabase()
	mockCache := cache.NewNoOpCache()
	cachedDB := NewCachedDB(mockDB, mockCache)

	// Test CreateBarcodeForShopper passthrough
	bfs := &model.BarcodeForShopper{
		EasyID:   123456,
		DeviceID: "device123",
	}

	id, err := cachedDB.CreateBarcodeForShopper(bfs)
	if err != nil {
		t.Fatalf("CreateBarcodeForShopper() error = %v", err)
	}

	if id != 1 {
		t.Errorf("CreateBarcodeForShopper() id = %d, want 1", id)
	}

	// Test CreateOfflineBarcodeTransaction passthrough
	obt := &model.OfflineBarcodeTransaction{
		KeyID:  "test123",
		EasyID: 123456,
	}

	id, err = cachedDB.CreateOfflineBarcodeTransaction(obt)
	if err != nil {
		t.Fatalf("CreateOfflineBarcodeTransaction() error = %v", err)
	}

	if id != 1 {
		t.Errorf("CreateOfflineBarcodeTransaction() id = %d, want 1", id)
	}
}
