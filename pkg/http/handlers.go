package http

import (
	"net/http"

	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"

	"rpay-barcode-service-go/pkg/service"
)

type HTTPHandlers struct {
	shopperLogic *service.ShopperLogic
}

func NewHTTPHandlers(shopperLogic *service.ShopperLogic) *HTTPHandlers {
	return &HTTPHandlers{
		shopperLogic: shopperLogic,
	}
}

// BarcodeGenerateSeedRequest represents the HTTP request for generating barcode seed
// @Description Request payload for generating barcode seed
type BarcodeGenerateSeedRequest struct {
	EasyID    int64  `json:"easyId" binding:"required" example:"123456" minimum:"1"`
	DeviceID  string `json:"deviceId" binding:"required" example:"device123" minLength:"1" maxLength:"255"`
	SessionID string `json:"sessionId" binding:"required" example:"session123" minLength:"1" maxLength:"255"`
	IPAddress string `json:"ipAddress" binding:"required" example:"***********"`
}

// BarcodeGenerateSeedResponse represents the HTTP response for generating barcode seed
// @Description Response payload for barcode seed generation
type BarcodeGenerateSeedResponse struct {
	KeyID         string `json:"keyId" example:"12345678901"`
	OtpSeed       string `json:"otpSeed" example:"JBSWY3DPEHPK3PXP"`
	MinExpiryDate string `json:"minExpiryDate" example:"2024-01-15T10:30:00Z"`
}

// ErrorResponse represents an error response
// @Description Error response payload
type ErrorResponse struct {
	Error   string `json:"error" example:"INVALID_REQUEST"`
	Message string `json:"message" example:"Invalid request format"`
}

// HealthResponse represents a health check response
// @Description Health check response payload
type HealthResponse struct {
	Status  string `json:"status" example:"UP"`
	Service string `json:"service" example:"rpay-barcode-service-go"`
}

// GetShopperBarcodeGenerateSeed handles HTTP requests for generating barcode seeds
// @Summary Generate offline barcode seed
// @Description Generates an offline barcode seed for a shopper. This endpoint creates or retrieves barcode seed details that can be used for offline barcode generation.
// @Tags Barcode
// @Accept json
// @Produce json
// @Param Authorization header string true "Bearer JWT token" default(Bearer )
// @Param request body BarcodeGenerateSeedRequest true "Barcode generation request"
// @Success 200 {object} BarcodeGenerateSeedResponse "Barcode seed generated successfully"
// @Failure 400 {object} ErrorResponse "Invalid request format"
// @Failure 401 {object} ErrorResponse "Unauthorized - Invalid or missing JWT token"
// @Failure 403 {object} ErrorResponse "Forbidden - Insufficient permissions"
// @Failure 500 {object} ErrorResponse "Internal server error"
// @Security BearerAuth
// @Router /api/v1/barcode/generate-seed [post]
func (h *HTTPHandlers) GetShopperBarcodeGenerateSeed(c *gin.Context) {
	var req BarcodeGenerateSeedRequest

	// Bind JSON request
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "INVALID_REQUEST",
			Message: "Invalid request format: " + err.Error(),
		})
		return
	}

	// Call business logic
	response, err := h.shopperLogic.GenerateOfflineDetails(
		req.EasyID,
		req.DeviceID,
		req.SessionID,
		req.IPAddress,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "GENERATION_FAILED",
			Message: "Failed to generate offline details: " + err.Error(),
		})
		return
	}

	// Convert to HTTP response
	httpResponse := BarcodeGenerateSeedResponse{
		KeyID:         response.KeyId,
		OtpSeed:       response.OtpSeed,
		MinExpiryDate: response.MinExpiryDate,
	}

	c.JSON(http.StatusOK, httpResponse)
}

// HealthCheck handles health check requests
// @Summary Health check endpoint
// @Description Returns the health status of the service
// @Tags Health
// @Produce json
// @Success 200 {object} HealthResponse "Service is healthy"
// @Router /health [get]
func (h *HTTPHandlers) HealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, HealthResponse{
		Status:  "UP",
		Service: "rpay-barcode-service-go",
	})
}

// SetupSwaggerRoutes sets up Swagger documentation routes
func (h *HTTPHandlers) SetupSwaggerRoutes(router *gin.Engine) {
	// Swagger documentation endpoint
	router.GET("/swagger/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// Redirect root to swagger docs
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/swagger/index.html")
	})
}
