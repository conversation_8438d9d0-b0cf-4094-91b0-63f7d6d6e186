package http

import (
	"context"
	"fmt"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"

	"rpay-barcode-service-go/pkg/config"
	"rpay-barcode-service-go/pkg/service"
	_ "rpay-barcode-service-go/docs" // Import for swagger docs
)

type HTTPServer struct {
	config   *config.Config
	handlers *HTTPHandlers
	server   *http.Server
}

func NewHTTPServer(cfg *config.Config, shopperLogic *service.ShopperLogic) *HTTPServer {
	handlers := NewHTTPHandlers(shopperLogic)

	return &HTTPServer{
		config:   cfg,
		handlers: handlers,
	}
}

func (s *HTTPServer) Start() error {
	// Set Gin mode
	gin.SetMode(gin.ReleaseMode)

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// CORS middleware for Swagger UI
	router.Use(func(c *gin.Context) {
		c.<PERSON>("Access-Control-Allow-Origin", "*")
		c<PERSON>Head<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// Setup Swagger routes (includes root redirect)
	s.handlers.SetupSwaggerRoutes(router)

	// JWT middleware
	jwtMiddleware := NewJWTMiddleware(s.config)

	// Public routes (no authentication required)
	router.GET("/health", s.handlers.HealthCheck)

	// API v1 routes with JWT authentication
	v1 := router.Group("/api/v1")
	v1.Use(jwtMiddleware.JWTAuth())
	{
		// Barcode generation endpoint
		v1.POST("/barcode/generate-seed", s.handlers.GetShopperBarcodeGenerateSeed)
	}

	// Create HTTP server
	s.server = &http.Server{
		Addr:    ":" + s.config.HTTPPort,
		Handler: router,
	}

	log.Printf("HTTP server starting on port %s", s.config.HTTPPort)
	log.Printf("Available endpoints:")
	log.Printf("  GET  / - Swagger UI (redirects to /swagger/index.html)")
	log.Printf("  GET  /swagger/index.html - Swagger UI")
	log.Printf("  GET  /health - Health check")
	log.Printf("  POST /api/v1/barcode/generate-seed - Generate barcode seed (requires JWT)")

	// Start server
	if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		return fmt.Errorf("failed to start HTTP server: %w", err)
	}

	return nil
}

func (s *HTTPServer) Stop(ctx context.Context) error {
	log.Println("Shutting down HTTP server...")
	return s.server.Shutdown(ctx)
}
