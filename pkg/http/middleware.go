package http

import (
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"

	"rpay-barcode-service-go/pkg/config"
)

type JWTMiddleware struct {
	config *config.Config
}

func NewJWTMiddleware(cfg *config.Config) *JWTMiddleware {
	return &JWTMiddleware{
		config: cfg,
	}
}

// JWTAuth validates JWT tokens for HTTP requests
func (j *JWTMiddleware) JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Missing Authorization header",
			})
			c.Abort()
			return
		}

		// Check Bearer prefix
		if !strings.HasPrefix(authHeader, j.config.Scheme) {
			c.<PERSON><PERSON>(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Invalid Authorization header format",
			})
			c.Abort()
			return
		}

		// Extract token
		tokenString := strings.TrimPrefix(authHeader, j.config.Scheme)
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Missing JWT token",
			})
			c.Abort()
			return
		}

		// Parse and validate token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// Validate signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return j.config.RPAYSecretKey, nil
		})

		if err != nil {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Invalid JWT token: " + err.Error(),
			})
			c.Abort()
			return
		}

		if !token.Valid {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Invalid JWT token",
			})
			c.Abort()
			return
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Invalid JWT claims",
			})
			c.Abort()
			return
		}

		// Validate expiration
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				c.JSON(http.StatusUnauthorized, ErrorResponse{
					Error:   "UNAUTHORIZED",
					Message: "JWT token has expired",
				})
				c.Abort()
				return
			}
		}

		// Validate subject
		if sub, ok := claims["sub"].(string); ok {
			if sub != j.config.RPAYSubject {
				c.JSON(http.StatusForbidden, ErrorResponse{
					Error:   "FORBIDDEN",
					Message: "Invalid subject in JWT token",
				})
				c.Abort()
				return
			}
		} else {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "UNAUTHORIZED",
				Message: "Missing subject in JWT token",
			})
			c.Abort()
			return
		}

		// Validate roles
		if roles, ok := claims["roles"].([]interface{}); ok {
			hasRequiredRole := false
			for _, role := range roles {
				if roleStr, ok := role.(string); ok && roleStr == j.config.BarcodeInternalRole {
					hasRequiredRole = true
					break
				}
			}
			if !hasRequiredRole {
				c.JSON(http.StatusForbidden, ErrorResponse{
					Error:   "FORBIDDEN",
					Message: "Insufficient permissions",
				})
				c.Abort()
				return
			}
		} else {
			c.JSON(http.StatusForbidden, ErrorResponse{
				Error:   "FORBIDDEN",
				Message: "Missing roles in JWT token",
			})
			c.Abort()
			return
		}

		// Store claims in context for later use
		c.Set("jwt_claims", claims)
		c.Next()
	}
}
