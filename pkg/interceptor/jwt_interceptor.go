package interceptor

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	"rpay-barcode-service-go/pkg/config"
)

type JWTInterceptor struct {
	config *config.Config
}

func NewJWTInterceptor(cfg *config.Config) *JWTInterceptor {
	return &JWTInterceptor{
		config: cfg,
	}
}

func (i *JWTInterceptor) UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(
		ctx context.Context,
		req interface{},
		info *grpc.UnaryServerInfo,
		handler grpc.UnaryHandler,
	) (interface{}, error) {
		startTime := time.Now()
		methodName := info.FullMethod

		// Extract metadata from context
		md, ok := metadata.FromIncomingContext(ctx)
		if !ok {
			i.logAccess(methodName, startTime, "No metadata found")
			return nil, status.Error(codes.PermissionDenied, "No metadata found")
		}

		// Get authorization header
		authHeaders := md.Get("authorization")
		if len(authHeaders) == 0 {
			i.logAccess(methodName, startTime, "JWT header not found")
			return nil, status.Error(codes.PermissionDenied, "JWT header not found")
		}

		authHeader := authHeaders[0]
		if !strings.HasPrefix(authHeader, i.config.Scheme) {
			i.logAccess(methodName, startTime, "Invalid authorization scheme")
			return nil, status.Error(codes.PermissionDenied, "Invalid authorization scheme")
		}

		// Extract JWT token
		tokenString := strings.TrimPrefix(authHeader, i.config.Scheme)
		tokenString = strings.TrimSpace(tokenString)
		if tokenString == "" {
			i.logAccess(methodName, startTime, "JWT token not found")
			return nil, status.Error(codes.PermissionDenied, "JWT token not found")
		}

		// Verify JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// Validate signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return i.config.RPAYSecretKey, nil
		})

		if err != nil {
			i.logAccess(methodName, startTime, fmt.Sprintf("Invalid JWT Token: %v", err))
			return nil, status.Error(codes.PermissionDenied, "Invalid JWT Token")
		}

		if !token.Valid {
			i.logAccess(methodName, startTime, "Invalid JWT Token")
			return nil, status.Error(codes.PermissionDenied, "Invalid JWT Token")
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			i.logAccess(methodName, startTime, "Invalid JWT claims")
			return nil, status.Error(codes.PermissionDenied, "Invalid JWT claims")
		}

		// Verify subject
		subject, ok := claims["sub"].(string)
		if !ok || subject != i.config.RPAYSubject {
			i.logAccess(methodName, startTime, "Invalid subject")
			return nil, status.Error(codes.PermissionDenied, "Invalid subject")
		}

		// Verify expiration
		if exp, ok := claims["exp"].(float64); ok {
			if time.Now().Unix() > int64(exp) {
				i.logAccess(methodName, startTime, "Token expired")
				return nil, status.Error(codes.PermissionDenied, "Token expired")
			}
		} else {
			i.logAccess(methodName, startTime, "No expiration time in token")
			return nil, status.Error(codes.PermissionDenied, "No expiration time in token")
		}

		// Check role permissions for internal service
		if strings.Contains(methodName, "ShopperBarcodeInternalService") {
			groups, ok := claims["groups"].([]interface{})
			if !ok {
				i.logAccess(methodName, startTime, "No groups in token")
				return nil, status.Error(codes.PermissionDenied, "No groups in token")
			}

			hasRole := false
			for _, group := range groups {
				if groupStr, ok := group.(string); ok && groupStr == i.config.BarcodeInternalRole {
					hasRole = true
					break
				}
			}

			if !hasRole {
				i.logAccess(methodName, startTime, "Insufficient permissions")
				return nil, status.Error(codes.PermissionDenied, "Insufficient permissions")
			}
		}

		// Call the handler
		resp, err := handler(ctx, req)

		// Log the result
		if err != nil {
			i.logAccess(methodName, startTime, fmt.Sprintf("Error: %v", err))
		} else {
			i.logAccess(methodName, startTime, "Success")
		}

		return resp, err
	}
}

func (i *JWTInterceptor) logAccess(methodName string, startTime time.Time, status string) {
	duration := time.Since(startTime)
	log.Printf("AccessLog: Method=%s, Duration=%v, Status=%s", methodName, duration, status)
}
