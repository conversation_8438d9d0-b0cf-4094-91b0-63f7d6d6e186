package generator

import (
	"crypto/sha256"
	"fmt"
	"math"
	"math/rand"
	"time"
)

type BarcodeTokenGenerator struct{}

func NewBarcodeTokenGenerator() *BarcodeTokenGenerator {
	return &BarcodeTokenGenerator{}
}

func (g *BarcodeTokenGenerator) GenerateBarcodeToken(easyId int64, targetTime time.Time) string {
	seedInput := fmt.Sprintf("%d-%d", easyId, targetTime.Unix())
	
	// Create SHA-256 hash
	digest := sha256.Sum256([]byte(seedInput))
	
	// Use first 16 bytes as seed for random number generator
	seed := int64(0)
	for i := 0; i < 8; i++ {
		seed = (seed << 8) | int64(digest[i])
	}
	
	// Create seeded random generator
	rng := rand.New(rand.NewSource(seed))
	
	// Generate barcode token between 1,000,000,000 and 9,999,999,999
	min := int64(1_000_000_000)
	max := int64(9_999_999_999)
	
	barcodeToken := min + int64(math.Abs(float64(rng.Int63())))%(max-min+1)
	
	return fmt.Sprintf("%d", barcodeToken)
}
