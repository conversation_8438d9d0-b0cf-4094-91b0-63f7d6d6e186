package generator

import (
	"encoding/base32"
	"strings"
	"testing"
)

func TestNewOtpSeedGenerator(t *testing.T) {
	opsLimit := 3
	memLimit := 32 * 1024
	parallelism := 4
	saltLength := 16

	generator := NewOtpSeedGenerator(opsLimit, memLimit, parallelism, saltLength)

	if generator == nil {
		t.Fatal("NewOtpSeedGenerator() returned nil")
	}

	if generator.opsLimit != uint32(opsLimit) {
		t.<PERSON>("NewOtpSeedGenerator() opsLimit = %d, want %d", generator.opsLimit, opsLimit)
	}

	if generator.memLimit != uint32(memLimit) {
		t.<PERSON><PERSON>rf("NewOtpSeedGenerator() memLimit = %d, want %d", generator.memLimit, memLimit)
	}

	if generator.parallelism != uint8(parallelism) {
		t.<PERSON><PERSON>("NewOtpSeedGenerator() parallelism = %d, want %d", generator.parallelism, parallelism)
	}

	if generator.saltLength != saltLength {
		t.<PERSON><PERSON><PERSON>("NewOtpSeedGenerator() saltLength = %d, want %d", generator.saltLength, saltLength)
	}
}

func TestOtpSeedGenerator_GenerateArgon2Hash(t *testing.T) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)

	tests := []struct {
		name               string
		sessionId          string
		easyId             int64
		resultArrayLength  int
		wantErr            bool
		expectedMinLength  int
	}{
		{
			name:              "Valid input standard case",
			sessionId:         "session123",
			easyId:            123456,
			resultArrayLength: 32,
			wantErr:           false,
			expectedMinLength: 40, // Base32 encoding of 32 bytes should be at least 40 chars
		},
		{
			name:              "Valid input with small result length",
			sessionId:         "test",
			easyId:            1,
			resultArrayLength: 16,
			wantErr:           false,
			expectedMinLength: 20, // Base32 encoding of 16 bytes
		},
		{
			name:              "Valid input with large result length",
			sessionId:         "longsession",
			easyId:            999999,
			resultArrayLength: 64,
			wantErr:           false,
			expectedMinLength: 80, // Base32 encoding of 64 bytes
		},
		{
			name:              "Empty session ID",
			sessionId:         "",
			easyId:            123,
			resultArrayLength: 32,
			wantErr:           false,
			expectedMinLength: 40,
		},
		{
			name:              "Zero easy ID",
			sessionId:         "session",
			easyId:            0,
			resultArrayLength: 32,
			wantErr:           false,
			expectedMinLength: 40,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := generator.GenerateArgon2Hash(tt.sessionId, tt.easyId, tt.resultArrayLength)

			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateArgon2Hash() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// Check that result is not empty
				if result == "" {
					t.Error("GenerateArgon2Hash() returned empty result")
				}

				// Check minimum length (Base32 encoded)
				if len(result) < tt.expectedMinLength {
					t.Errorf("GenerateArgon2Hash() result length = %d, want at least %d", len(result), tt.expectedMinLength)
				}

				// Check that result is valid Base32
				_, err := base32.StdEncoding.DecodeString(result)
				if err != nil {
					t.Errorf("GenerateArgon2Hash() result is not valid Base32: %v", err)
				}

				// Check that result contains only valid Base32 characters
				validChars := "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567="
				for _, char := range result {
					if !strings.ContainsRune(validChars, char) {
						t.Errorf("GenerateArgon2Hash() result contains invalid Base32 character: %c", char)
					}
				}
			}
		})
	}
}

func TestOtpSeedGenerator_Consistency(t *testing.T) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)

	sessionId := "consistent_test"
	easyId := int64(12345)
	resultArrayLength := 32

	// Generate multiple hashes with same input
	results := make([]string, 5)
	for i := 0; i < 5; i++ {
		result, err := generator.GenerateArgon2Hash(sessionId, easyId, resultArrayLength)
		if err != nil {
			t.Fatalf("GenerateArgon2Hash() error = %v", err)
		}
		results[i] = result
	}

	// All results should be different due to random salt
	for i := 0; i < len(results); i++ {
		for j := i + 1; j < len(results); j++ {
			if results[i] == results[j] {
				t.Errorf("GenerateArgon2Hash() produced duplicate results: %s", results[i])
			}
		}
	}
}

func TestOtpSeedGenerator_DifferentInputs(t *testing.T) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)
	resultArrayLength := 32

	// Test different session IDs
	result1, err1 := generator.GenerateArgon2Hash("session1", 123, resultArrayLength)
	result2, err2 := generator.GenerateArgon2Hash("session2", 123, resultArrayLength)

	if err1 != nil || err2 != nil {
		t.Fatalf("GenerateArgon2Hash() errors: %v, %v", err1, err2)
	}

	if result1 == result2 {
		t.Error("Different session IDs should produce different results")
	}

	// Test different easy IDs
	result3, err3 := generator.GenerateArgon2Hash("session1", 456, resultArrayLength)
	if err3 != nil {
		t.Fatalf("GenerateArgon2Hash() error = %v", err3)
	}

	if result1 == result3 {
		t.Error("Different easy IDs should produce different results")
	}
}

func TestOtpSeedGenerator_generateSaltBytes(t *testing.T) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)

	tests := []struct {
		name       string
		saltLength int
		wantErr    bool
	}{
		{
			name:       "Standard salt length",
			saltLength: 16,
			wantErr:    false,
		},
		{
			name:       "Small salt length",
			saltLength: 8,
			wantErr:    false,
		},
		{
			name:       "Large salt length",
			saltLength: 32,
			wantErr:    false,
		},
		{
			name:       "Zero salt length",
			saltLength: 0,
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			salt, err := generator.generateSaltBytes(tt.saltLength)

			if (err != nil) != tt.wantErr {
				t.Errorf("generateSaltBytes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if len(salt) != tt.saltLength {
					t.Errorf("generateSaltBytes() salt length = %d, want %d", len(salt), tt.saltLength)
				}
			}
		})
	}
}

func TestOtpSeedGenerator_generateSeed(t *testing.T) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)

	sessionId := "test_session"
	easyId := int64(12345)

	seed := generator.generateSeed(sessionId, easyId)

	// Check that seed contains session ID
	if !strings.Contains(seed, sessionId) {
		t.Errorf("generateSeed() seed doesn't contain session ID: %s", seed)
	}

	// Check that seed is not empty
	if seed == "" {
		t.Error("generateSeed() returned empty seed")
	}
}

func BenchmarkOtpSeedGenerator_GenerateArgon2Hash(b *testing.B) {
	generator := NewOtpSeedGenerator(3, 32*1024, 4, 16)
	sessionId := "benchmark_session"
	easyId := int64(123456)
	resultArrayLength := 32

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := generator.GenerateArgon2Hash(sessionId, easyId, resultArrayLength)
		if err != nil {
			b.Fatalf("GenerateArgon2Hash() error = %v", err)
		}
	}
}
