package generator

import (
	"testing"
	"time"
)

func TestKeyIdGenerator_GenerateKeyId(t *testing.T) {
	generator := NewKeyIdGenerator()

	tests := []struct {
		name        string
		sessionId   string
		easyId      int64
		keyIdLength int
		wantErr     bool
	}{
		{
			name:        "Valid input with standard length",
			sessionId:   "session123",
			easyId:      123456,
			keyIdLength: 11,
			wantErr:     false,
		},
		{
			name:        "Valid input with short length",
			sessionId:   "test",
			easyId:      1,
			keyIdLength: 5,
			wantErr:     false,
		},
		{
			name:        "Valid input with long length",
			sessionId:   "longsessionid",
			easyId:      999999999,
			keyIdLength: 20,
			wantErr:     false,
		},
		{
			name:        "Empty session ID",
			sessionId:   "",
			easyId:      123,
			keyIdLength: 10,
			wantErr:     false,
		},
		{
			name:        "Zero easy ID",
			sessionId:   "session",
			easyId:      0,
			keyIdLength: 10,
			wantErr:     false,
		},
		{
			name:        "Zero length",
			sessionId:   "session",
			easyId:      123,
			keyIdLength: 0,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			keyId, err := generator.GenerateKeyId(tt.sessionId, tt.easyId, tt.keyIdLength)
			
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateKeyId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			
			if !tt.wantErr {
				// Check that the key ID has the correct length
				if len(keyId) != tt.keyIdLength {
					t.Errorf("GenerateKeyId() keyId length = %d, want %d", len(keyId), tt.keyIdLength)
				}
				
				// Check that the key ID contains only digits
				for _, char := range keyId {
					if char < '0' || char > '9' {
						t.Errorf("GenerateKeyId() keyId contains non-digit character: %c", char)
					}
				}
			}
		})
	}
}

func TestKeyIdGenerator_Deterministic(t *testing.T) {
	generator := NewKeyIdGenerator()
	
	// Mock time to ensure deterministic results
	sessionId := "test_session"
	easyId := int64(12345)
	keyIdLength := 10
	
	// Generate the same key ID multiple times within a short time window
	// Note: This test might be flaky due to time dependency, but it tests the deterministic nature
	keyIds := make([]string, 5)
	for i := 0; i < 5; i++ {
		keyId, err := generator.GenerateKeyId(sessionId, easyId, keyIdLength)
		if err != nil {
			t.Fatalf("GenerateKeyId() error = %v", err)
		}
		keyIds[i] = keyId
		time.Sleep(time.Millisecond) // Small delay to potentially change timestamp
	}
	
	// All key IDs should be different due to timestamp dependency
	for i := 0; i < len(keyIds); i++ {
		for j := i + 1; j < len(keyIds); j++ {
			if keyIds[i] == keyIds[j] {
				t.Logf("KeyIds[%d] = %s, KeyIds[%d] = %s", i, keyIds[i], j, keyIds[j])
				// This is expected behavior since timestamp changes
			}
		}
	}
}

func TestKeyIdGenerator_DifferentInputs(t *testing.T) {
	generator := NewKeyIdGenerator()
	keyIdLength := 10
	
	// Test that different inputs produce different outputs
	keyId1, err1 := generator.GenerateKeyId("session1", 123, keyIdLength)
	keyId2, err2 := generator.GenerateKeyId("session2", 123, keyIdLength)
	keyId3, err3 := generator.GenerateKeyId("session1", 456, keyIdLength)
	
	if err1 != nil || err2 != nil || err3 != nil {
		t.Fatalf("GenerateKeyId() errors: %v, %v, %v", err1, err2, err3)
	}
	
	// Different session IDs should produce different key IDs
	if keyId1 == keyId2 {
		t.Errorf("Different session IDs produced same key ID: %s", keyId1)
	}
	
	// Different easy IDs should produce different key IDs
	if keyId1 == keyId3 {
		t.Errorf("Different easy IDs produced same key ID: %s", keyId1)
	}
}

func TestKeyIdGenerator_generateSeed(t *testing.T) {
	generator := NewKeyIdGenerator()
	
	sessionId := "test_session"
	easyId := int64(12345)
	
	seed := generator.generateSeed(sessionId, easyId)
	
	// Check that seed contains session ID
	if len(seed) < len(sessionId) {
		t.Errorf("generateSeed() seed too short: %s", seed)
	}
	
	// Check that seed starts with session ID
	if seed[:len(sessionId)] != sessionId {
		t.Errorf("generateSeed() seed doesn't start with session ID: %s", seed)
	}
}

func BenchmarkKeyIdGenerator_GenerateKeyId(b *testing.B) {
	generator := NewKeyIdGenerator()
	sessionId := "benchmark_session"
	easyId := int64(123456)
	keyIdLength := 11
	
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := generator.GenerateKeyId(sessionId, easyId, keyIdLength)
		if err != nil {
			b.Fatalf("GenerateKeyId() error = %v", err)
		}
	}
}
