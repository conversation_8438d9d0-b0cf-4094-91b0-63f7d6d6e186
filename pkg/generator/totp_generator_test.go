package generator

import (
	"strings"
	"testing"
	"time"
)

func TestNewTotpGenerator(t *testing.T) {
	generator := NewTotpGenerator()
	if generator == nil {
		t.Fatal("NewTotpGenerator() returned nil")
	}
}

func TestTotpGenerator_GenerateTotp(t *testing.T) {
	generator := NewTotpGenerator()

	// Create a valid Base32 encoded seed
	seed := "JBSWY3DPEHPK3PXP"             // "Hello World" in Base32
	targetTime := time.Unix(1234567890, 0) // Fixed time for deterministic testing
	timeStep := 30 * time.Second
	digits := 6

	tests := []struct {
		name       string
		otpSeed    string
		targetTime time.Time
		timeStep   time.Duration
		digits     int
		wantErr    bool
		wantLength int
	}{
		{
			name:       "Valid input standard case",
			otpSeed:    seed,
			targetTime: targetTime,
			timeStep:   timeStep,
			digits:     digits,
			wantErr:    false,
			wantLength: 6,
		},
		{
			name:       "Valid input with 8 digits",
			otpSeed:    seed,
			targetTime: targetTime,
			timeStep:   timeStep,
			digits:     8,
			wantErr:    false,
			wantLength: 8,
		},
		{
			name:       "Valid input with 4 digits",
			otpSeed:    seed,
			targetTime: targetTime,
			timeStep:   timeStep,
			digits:     4,
			wantErr:    false,
			wantLength: 4,
		},
		{
			name:       "Empty OTP seed",
			otpSeed:    "",
			targetTime: targetTime,
			timeStep:   timeStep,
			digits:     digits,
			wantErr:    true,
			wantLength: 0,
		},
		{
			name:       "Invalid Base32 seed",
			otpSeed:    "INVALID_BASE32_!@#",
			targetTime: targetTime,
			timeStep:   timeStep,
			digits:     digits,
			wantErr:    true,
			wantLength: 0,
		},
		{
			name:       "Different time step",
			otpSeed:    seed,
			targetTime: targetTime,
			timeStep:   60 * time.Second,
			digits:     digits,
			wantErr:    false,
			wantLength: 6,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := generator.GenerateTotp(tt.otpSeed, tt.targetTime, tt.timeStep, tt.digits)

			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateTotp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// Check length
				if len(result) != tt.wantLength {
					t.Errorf("GenerateTotp() result length = %d, want %d", len(result), tt.wantLength)
				}

				// Check that result contains only digits
				for _, char := range result {
					if char < '0' || char > '9' {
						t.Errorf("GenerateTotp() result contains non-digit character: %c", char)
					}
				}

				// Check that result is not empty
				if result == "" {
					t.Error("GenerateTotp() returned empty result")
				}
			}
		})
	}
}

func TestTotpGenerator_Deterministic(t *testing.T) {
	generator := NewTotpGenerator()

	seed := "JBSWY3DPEHPK3PXP"
	targetTime := time.Unix(1234567890, 0)
	timeStep := 30 * time.Second
	digits := 6

	// Generate TOTP multiple times with same parameters
	results := make([]string, 5)
	for i := 0; i < 5; i++ {
		result, err := generator.GenerateTotp(seed, targetTime, timeStep, digits)
		if err != nil {
			t.Fatalf("GenerateTotp() error = %v", err)
		}
		results[i] = result
	}

	// All results should be identical (deterministic)
	for i := 1; i < len(results); i++ {
		if results[0] != results[i] {
			t.Errorf("GenerateTotp() not deterministic: %s != %s", results[0], results[i])
		}
	}
}

func TestTotpGenerator_DifferentTimes(t *testing.T) {
	generator := NewTotpGenerator()

	seed := "JBSWY3DPEHPK3PXP"
	timeStep := 30 * time.Second
	digits := 6

	// Generate TOTPs for different times
	time1 := time.Unix(1234567890, 0)
	time2 := time.Unix(1234567890+30, 0) // 30 seconds later (next time step)
	time3 := time.Unix(1234567890+60, 0) // 60 seconds later (2 time steps)

	totp1, err1 := generator.GenerateTotp(seed, time1, timeStep, digits)
	totp2, err2 := generator.GenerateTotp(seed, time2, timeStep, digits)
	totp3, err3 := generator.GenerateTotp(seed, time3, timeStep, digits)

	if err1 != nil || err2 != nil || err3 != nil {
		t.Fatalf("GenerateTotp() errors: %v, %v, %v", err1, err2, err3)
	}

	// TOTPs for different time steps should be different
	if totp1 == totp2 {
		t.Errorf("TOTPs for different time steps should be different: %s == %s", totp1, totp2)
	}
	if totp2 == totp3 {
		t.Errorf("TOTPs for different time steps should be different: %s == %s", totp2, totp3)
	}
	if totp1 == totp3 {
		t.Errorf("TOTPs for different time steps should be different: %s == %s", totp1, totp3)
	}
}

func TestTotpGenerator_DifferentSeeds(t *testing.T) {
	generator := NewTotpGenerator()

	targetTime := time.Unix(1234567890, 0)
	timeStep := 30 * time.Second
	digits := 6

	// Different valid Base32 seeds
	seed1 := "JBSWY3DPEHPK3PXP"
	seed2 := "GEZDGNBVGY3TQOJQ" // Different seed

	totp1, err1 := generator.GenerateTotp(seed1, targetTime, timeStep, digits)
	totp2, err2 := generator.GenerateTotp(seed2, targetTime, timeStep, digits)

	if err1 != nil || err2 != nil {
		t.Fatalf("GenerateTotp() errors: %v, %v", err1, err2)
	}

	// Different seeds should produce different TOTPs
	if totp1 == totp2 {
		t.Errorf("Different seeds should produce different TOTPs: %s == %s", totp1, totp2)
	}
}

func TestTotpGenerator_LeadingZeros(t *testing.T) {
	generator := NewTotpGenerator()

	// Use a seed and time that might produce a TOTP with leading zeros
	seed := "JBSWY3DPEHPK3PXP"
	digits := 6

	// Test multiple times to increase chance of getting leading zeros
	for i := 0; i < 100; i++ {
		targetTime := time.Unix(int64(i*30), 0)
		totp, err := generator.GenerateTotp(seed, targetTime, 30*time.Second, digits)

		if err != nil {
			t.Fatalf("GenerateTotp() error = %v", err)
		}

		// Check that TOTP has correct length even with leading zeros
		if len(totp) != digits {
			t.Errorf("GenerateTotp() length = %d, want %d, totp = %s", len(totp), digits, totp)
		}

		// If we find a TOTP with leading zeros, verify it's formatted correctly
		if strings.HasPrefix(totp, "0") {
			t.Logf("Found TOTP with leading zero: %s", totp)
			// This is expected and correct behavior
		}
	}
}

func TestTotpGenerator_EdgeCases(t *testing.T) {
	generator := NewTotpGenerator()

	seed := "JBSWY3DPEHPK3PXP"
	timeStep := 30 * time.Second

	tests := []struct {
		name       string
		targetTime time.Time
		digits     int
		wantErr    bool
	}{
		{
			name:       "Unix epoch time",
			targetTime: time.Unix(0, 0),
			digits:     6,
			wantErr:    false,
		},
		{
			name:       "Far future time",
			targetTime: time.Unix(2147483647, 0), // Max 32-bit timestamp
			digits:     6,
			wantErr:    false,
		},
		{
			name:       "1 digit TOTP",
			targetTime: time.Unix(1234567890, 0),
			digits:     1,
			wantErr:    false,
		},
		{
			name:       "10 digit TOTP",
			targetTime: time.Unix(1234567890, 0),
			digits:     10,
			wantErr:    false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := generator.GenerateTotp(seed, tt.targetTime, timeStep, tt.digits)

			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateTotp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if len(result) != tt.digits {
					t.Errorf("GenerateTotp() result length = %d, want %d", len(result), tt.digits)
				}
			}
		})
	}
}

func BenchmarkTotpGenerator_GenerateTotp(b *testing.B) {
	generator := NewTotpGenerator()
	seed := "JBSWY3DPEHPK3PXP"
	targetTime := time.Unix(1234567890, 0)
	timeStep := 30 * time.Second
	digits := 6

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := generator.GenerateTotp(seed, targetTime, timeStep, digits)
		if err != nil {
			b.Fatalf("GenerateTotp() error = %v", err)
		}
	}
}
