package generator

import (
	"crypto/rand"
	"encoding/base32"
	"fmt"
	"time"

	"golang.org/x/crypto/argon2"
)

type OtpSeedGenerator struct {
	opsLimit    uint32
	memLimit    uint32
	parallelism uint8
	saltLength  int
}

func NewOtpSeedGenerator(opsLimit, memLimit int, parallelism int, saltLength int) *OtpSeedGenerator {
	return &OtpSeedGenerator{
		opsLimit:    uint32(opsLimit),
		memLimit:    uint32(memLimit),
		parallelism: uint8(parallelism),
		saltLength:  saltLength,
	}
}

func (g *OtpSeedGenerator) GenerateArgon2Hash(sessionId string, easyId int64, resultArrayLength int) (string, error) {
	// Generate salt
	salt, err := g.generateSaltBytes(g.saltLength)
	if err != nil {
		return "", fmt.Errorf("failed to generate salt: %w", err)
	}

	// Generate seed
	seed := g.generateSeed(sessionId, easyId)

	// Generate Argon2 hash
	result := argon2.IDKey([]byte(seed), salt, g.opsLimit, g.memLimit, g.parallelism, uint32(resultArrayLength))

	// Encode to Base32
	return base32.StdEncoding.EncodeToString(result), nil
}

func (g *OtpSeedGenerator) generateSeed(sessionId string, easyId int64) string {
	timestampPlusId := time.Now().Unix() + easyId
	return fmt.Sprintf("%s%d", sessionId, timestampPlusId)
}

func (g *OtpSeedGenerator) generateSaltBytes(length int) ([]byte, error) {
	salt := make([]byte, length)
	_, err := rand.Read(salt)
	if err != nil {
		return nil, err
	}
	return salt, nil
}
