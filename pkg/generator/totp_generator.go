package generator

import (
	"crypto/hmac"
	"crypto/sha512"
	"encoding/base32"
	"encoding/binary"
	"fmt"
	"math"
	"time"
)

type TotpGenerator struct{}

func NewTotpGenerator() *TotpGenerator {
	return &TotpGenerator{}
}

func (g *TotpGenerator) GenerateTotp(otpSeed string, targetTime time.Time, timeStep time.Duration, digits int) (string, error) {
	if otpSeed == "" {
		return "", fmt.<PERSON><PERSON><PERSON>("otpSeed cannot be empty")
	}

	// Decode Base32 seed
	keyBytes, err := base32.StdEncoding.DecodeString(otpSeed)
	if err != nil {
		return "", fmt.Errorf("failed to decode otpSeed: %w", err)
	}

	// Calculate time counter
	timeCounter := targetTime.Unix() / int64(timeStep.Seconds())

	// Convert time counter to 8-byte big-endian
	timeBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timeBytes, uint64(timeCounter))

	// Create HMAC-SHA512
	mac := hmac.New(sha512.New, keyBytes)
	mac.Write(timeBytes)
	hmacResult := mac.Sum(nil)

	// Dynamic truncation
	offset := hmacResult[len(hmacResult)-1] & 0x0f

	// Extract 4 bytes starting from offset
	binaryCode := (int(hmacResult[offset])&0x7f)<<24 |
		(int(hmacResult[offset+1])&0xff)<<16 |
		(int(hmacResult[offset+2])&0xff)<<8 |
		(int(hmacResult[offset+3]) & 0xff)

	// Generate TOTP
	totp := binaryCode % int(math.Pow10(digits))

	// Format with leading zeros
	format := fmt.Sprintf("%%0%dd", digits)
	return fmt.Sprintf(format, totp), nil
}
