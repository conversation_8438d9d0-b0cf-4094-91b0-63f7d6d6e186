package generator

import (
	"crypto/sha256"
	"fmt"
	"strconv"
	"strings"
	"time"
)

type KeyIdGenerator struct{}

func NewKeyIdGenerator() *KeyIdGenerator {
	return &KeyIdGenerator{}
}

func (g *KeyIdGenerator) GenerateKeyId(sessionId string, easyId int64, keyIdLength int) (string, error) {
	seed := g.generateSeed(sessionId, easyId)

	// Create SHA-256 hash of the seed
	hash := sha256.Sum256([]byte(seed))

	// Use the hash as seed for deterministic random generation
	// Create a simple PRNG using the hash
	var keyIdBuilder strings.Builder

	for i := 0; i < keyIdLength; i++ {
		// Use different parts of the hash for each digit
		hashIndex := i % len(hash)
		digit := int(hash[hashIndex]) % 10
		keyIdBuilder.WriteString(strconv.Itoa(digit))
	}

	return keyIdBuilder.String(), nil
}

func (g *KeyIdGenerator) generateSeed(sessionId string, easyId int64) string {
	timestampPlusId := time.Now().Unix() + easyId
	return fmt.Sprintf("%s%d", sessionId, timestampPlusId)
}
