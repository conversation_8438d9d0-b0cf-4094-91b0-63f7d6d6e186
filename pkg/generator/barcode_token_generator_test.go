package generator

import (
	"strconv"
	"testing"
	"time"
)

func TestNewBarcodeTokenGenerator(t *testing.T) {
	generator := NewBarcodeTokenGenerator()

	if generator == nil {
		t.Error("NewBarcodeTokenGenerator() returned nil")
	}
}

func TestBarcodeTokenGenerator_GenerateBarcodeToken(t *testing.T) {
	generator := NewBarcodeTokenGenerator()

	tests := []struct {
		name       string
		easyId     int64
		targetTime time.Time
	}{
		{
			name:       "Standard case",
			easyId:     123456,
			targetTime: time.Unix(1640995200, 0), // 2022-01-01 00:00:00 UTC
		},
		{
			name:       "Different easyId",
			easyId:     654321,
			targetTime: time.Unix(1640995200, 0),
		},
		{
			name:       "Different time",
			easyId:     123456,
			targetTime: time.Unix(1672531200, 0), // 2023-01-01 00:00:00 UTC
		},
		{
			name:       "Zero easyId",
			easyId:     0,
			targetTime: time.Unix(1640995200, 0),
		},
		{
			name:       "Large easyId",
			easyId:     9999999999,
			targetTime: time.Unix(1640995200, 0),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := generator.GenerateBarcodeToken(tt.easyId, tt.targetTime)

			// Check that result is a valid number string
			if _, err := strconv.ParseInt(result, 10, 64); err != nil {
				t.Errorf("GenerateBarcodeToken() = %s, should be a valid number", result)
			}

			// Check that result is within expected range (10 digits)
			if len(result) != 10 {
				t.Errorf("GenerateBarcodeToken() length = %d, want 10", len(result))
			}

			// Check that result starts with digit 1-9 (not 0)
			if result[0] < '1' || result[0] > '9' {
				t.Errorf("GenerateBarcodeToken() = %s, should start with digit 1-9", result)
			}
		})
	}
}

func TestBarcodeTokenGenerator_Deterministic(t *testing.T) {
	generator := NewBarcodeTokenGenerator()

	easyId := int64(123456)
	targetTime := time.Unix(1640995200, 0) // Fixed time

	// Generate multiple times with same input
	results := make([]string, 5)
	for i := 0; i < 5; i++ {
		results[i] = generator.GenerateBarcodeToken(easyId, targetTime)
	}

	// All results should be identical (deterministic)
	for i := 1; i < len(results); i++ {
		if results[0] != results[i] {
			t.Errorf("GenerateBarcodeToken() not deterministic: results[0] = %s, results[%d] = %s", results[0], i, results[i])
		}
	}
}

func TestBarcodeTokenGenerator_DifferentInputs(t *testing.T) {
	generator := NewBarcodeTokenGenerator()

	baseTime := time.Unix(1640995200, 0)

	// Test different easyIds produce different results
	result1 := generator.GenerateBarcodeToken(123456, baseTime)
	result2 := generator.GenerateBarcodeToken(654321, baseTime)

	if result1 == result2 {
		t.Errorf("Different easyIds should produce different tokens: %s == %s", result1, result2)
	}

	// Test different times produce different results
	result3 := generator.GenerateBarcodeToken(123456, baseTime)
	result4 := generator.GenerateBarcodeToken(123456, baseTime.Add(time.Hour))

	if result3 == result4 {
		t.Errorf("Different times should produce different tokens: %s == %s", result3, result4)
	}
}

func TestBarcodeTokenGenerator_Range(t *testing.T) {
	generator := NewBarcodeTokenGenerator()

	// Test multiple generations to ensure they're all in valid range
	for i := 0; i < 100; i++ {
		easyId := int64(i + 1)
		targetTime := time.Unix(1640995200+int64(i*3600), 0) // Different times

		result := generator.GenerateBarcodeToken(easyId, targetTime)

		// Parse as integer
		value, err := strconv.ParseInt(result, 10, 64)
		if err != nil {
			t.Fatalf("Invalid number generated: %s", result)
		}

		// Check range: 1,000,000,000 to 9,999,999,999
		if value < 1_000_000_000 || value > 9_999_999_999 {
			t.Errorf("Generated value %d out of range [1000000000, 9999999999]", value)
		}
	}
}
