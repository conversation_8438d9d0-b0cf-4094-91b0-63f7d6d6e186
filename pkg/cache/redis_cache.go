package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"

	"rpay-barcode-service-go/pkg/model"
)

// CacheInterface defines the cache operations
type CacheInterface interface {
	GetBarcodeSeedDetailsByKeyID(ctx context.Context, keyID string) (*model.BarcodeSeedDetails, error)
	GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx context.Context, easyID int64, deviceID string) (*model.BarcodeSeedDetails, error)
	SetBarcodeSeedDetails(ctx context.Context, details *model.BarcodeSeedDetails) error
	DeleteBarcodeSeedDetails(ctx context.Context, keyID string, easyID int64, deviceID string) error
	Close() error
}

// RedisCache implements CacheInterface using dual Redis endpoints for redundancy with auto-reconnection
type RedisCache struct {
	primaryClient   *redis.Client
	secondaryClient *redis.Client
	ttl             time.Duration

	// Connection configuration for auto-reconnection
	primaryAddr   string
	secondaryAddr string
	password      string
	db            int

	// Health monitoring
	primaryHealthy   bool
	secondaryHealthy bool
	healthMutex      sync.RWMutex

	// Auto-reconnection
	stopHealthCheck chan struct{}
	healthCheckDone chan struct{}
}

// NewRedisCache creates a new Redis cache instance with dual endpoints for redundancy
func NewRedisCache(primaryAddr, secondaryAddr, password string, db int, ttl time.Duration) *RedisCache {
	// Create primary Redis client
	primaryClient := redis.NewClient(&redis.Options{
		Addr:     primaryAddr,
		Password: password,
		DB:       db,
	})

	// Create secondary Redis client
	var secondaryClient *redis.Client
	if secondaryAddr != "" && secondaryAddr != primaryAddr {
		secondaryClient = redis.NewClient(&redis.Options{
			Addr:     secondaryAddr,
			Password: password,
			DB:       db,
		})
	}

	// Test connections
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	primaryHealthy := false
	secondaryHealthy := false

	// Test primary connection
	if _, err := primaryClient.Ping(ctx).Result(); err != nil {
		log.Printf("Warning: Primary Redis connection failed (%s): %v", primaryAddr, err)
	} else {
		primaryHealthy = true
		log.Printf("Primary Redis cache connected successfully (%s)", primaryAddr)
	}

	// Test secondary connection if configured
	if secondaryClient != nil {
		if _, err := secondaryClient.Ping(ctx).Result(); err != nil {
			log.Printf("Warning: Secondary Redis connection failed (%s): %v", secondaryAddr, err)
		} else {
			secondaryHealthy = true
			log.Printf("Secondary Redis cache connected successfully (%s)", secondaryAddr)
		}
	}

	// If neither Redis instance is available, disable caching
	if !primaryHealthy && !secondaryHealthy {
		log.Printf("Cache will be disabled - no Redis instances available")
		if primaryClient != nil {
			primaryClient.Close()
		}
		if secondaryClient != nil {
			secondaryClient.Close()
		}
		return &RedisCache{
			primaryClient:   nil,
			secondaryClient: nil,
			ttl:             ttl,
		}
	}

	// Clean up failed connections
	if !primaryHealthy {
		primaryClient.Close()
		primaryClient = nil
	}
	if !secondaryHealthy && secondaryClient != nil {
		secondaryClient.Close()
		secondaryClient = nil
	}

	log.Printf("Redis cache initialized with %d healthy endpoint(s)",
		func() int {
			count := 0
			if primaryHealthy {
				count++
			}
			if secondaryHealthy {
				count++
			}
			return count
		}())

	cache := &RedisCache{
		primaryClient:    primaryClient,
		secondaryClient:  secondaryClient,
		ttl:              ttl,
		primaryAddr:      primaryAddr,
		secondaryAddr:    secondaryAddr,
		password:         password,
		db:               db,
		primaryHealthy:   primaryHealthy,
		secondaryHealthy: secondaryHealthy,
		stopHealthCheck:  make(chan struct{}),
		healthCheckDone:  make(chan struct{}),
	}

	// Start health monitoring goroutine
	go cache.startHealthMonitoring()

	return cache
}

// startHealthMonitoring starts a background goroutine to monitor Redis health and auto-reconnect
func (r *RedisCache) startHealthMonitoring() {
	defer close(r.healthCheckDone)

	ticker := time.NewTicker(30 * time.Second) // Check every 30 seconds
	defer ticker.Stop()

	log.Println("Redis health monitoring started")

	for {
		select {
		case <-r.stopHealthCheck:
			log.Println("Redis health monitoring stopped")
			return
		case <-ticker.C:
			r.checkAndReconnect()
		}
	}
}

// checkAndReconnect checks the health of Redis connections and reconnects if needed
func (r *RedisCache) checkAndReconnect() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	r.healthMutex.Lock()
	defer r.healthMutex.Unlock()

	// Check primary Redis
	if r.primaryAddr != "" {
		if r.primaryClient == nil {
			// Try to reconnect
			log.Printf("Attempting to reconnect to primary Redis: %s", r.primaryAddr)
			client := redis.NewClient(&redis.Options{
				Addr:     r.primaryAddr,
				Password: r.password,
				DB:       r.db,
			})

			if _, err := client.Ping(ctx).Result(); err == nil {
				r.primaryClient = client
				r.primaryHealthy = true
				log.Printf("Successfully reconnected to primary Redis: %s", r.primaryAddr)
			} else {
				client.Close()
				log.Printf("Failed to reconnect to primary Redis: %v", err)
			}
		} else {
			// Check existing connection
			if _, err := r.primaryClient.Ping(ctx).Result(); err != nil {
				log.Printf("Primary Redis connection lost: %v", err)
				r.primaryClient.Close()
				r.primaryClient = nil
				r.primaryHealthy = false
			} else if !r.primaryHealthy {
				r.primaryHealthy = true
				log.Printf("Primary Redis connection restored: %s", r.primaryAddr)
			}
		}
	}

	// Check secondary Redis
	if r.secondaryAddr != "" && r.secondaryAddr != r.primaryAddr {
		if r.secondaryClient == nil {
			// Try to reconnect
			log.Printf("Attempting to reconnect to secondary Redis: %s", r.secondaryAddr)
			client := redis.NewClient(&redis.Options{
				Addr:     r.secondaryAddr,
				Password: r.password,
				DB:       r.db,
			})

			if _, err := client.Ping(ctx).Result(); err == nil {
				r.secondaryClient = client
				r.secondaryHealthy = true
				log.Printf("Successfully reconnected to secondary Redis: %s", r.secondaryAddr)
			} else {
				client.Close()
				log.Printf("Failed to reconnect to secondary Redis: %v", err)
			}
		} else {
			// Check existing connection
			if _, err := r.secondaryClient.Ping(ctx).Result(); err != nil {
				log.Printf("Secondary Redis connection lost: %v", err)
				r.secondaryClient.Close()
				r.secondaryClient = nil
				r.secondaryHealthy = false
			} else if !r.secondaryHealthy {
				r.secondaryHealthy = true
				log.Printf("Secondary Redis connection restored: %s", r.secondaryAddr)
			}
		}
	}
}

// isEnabled returns true if at least one Redis client is available
func (r *RedisCache) isEnabled() bool {
	r.healthMutex.RLock()
	defer r.healthMutex.RUnlock()
	return r.primaryClient != nil || r.secondaryClient != nil
}

// executeOnAvailableClients executes a function on available Redis clients
// Returns the first successful result, or the last error if all fail
func (r *RedisCache) executeOnAvailableClients(ctx context.Context, operation func(*redis.Client) error) error {
	r.healthMutex.RLock()
	primaryClient := r.primaryClient
	secondaryClient := r.secondaryClient
	r.healthMutex.RUnlock()

	var lastErr error

	// Try primary client first
	if primaryClient != nil {
		if err := operation(primaryClient); err == nil {
			return nil // Success
		} else {
			lastErr = err
			log.Printf("Primary Redis operation failed: %v", err)
		}
	}

	// Try secondary client if primary failed
	if secondaryClient != nil {
		if err := operation(secondaryClient); err == nil {
			return nil // Success
		} else {
			lastErr = err
			log.Printf("Secondary Redis operation failed: %v", err)
		}
	}

	return lastErr // All clients failed
}

// getFromAvailableClients tries to get a value from available Redis clients
func (r *RedisCache) getFromAvailableClients(ctx context.Context, key string) (string, error) {
	r.healthMutex.RLock()
	primaryClient := r.primaryClient
	secondaryClient := r.secondaryClient
	r.healthMutex.RUnlock()

	var lastErr error

	// Try primary client first
	if primaryClient != nil {
		val, err := primaryClient.Get(ctx, key).Result()
		if err == nil {
			return val, nil // Success
		} else if err != redis.Nil {
			lastErr = err
			log.Printf("Primary Redis GET failed for key %s: %v", key, err)
		} else {
			return "", redis.Nil // Not found
		}
	}

	// Try secondary client if primary failed
	if secondaryClient != nil {
		val, err := secondaryClient.Get(ctx, key).Result()
		if err == nil {
			return val, nil // Success
		} else if err != redis.Nil {
			lastErr = err
			log.Printf("Secondary Redis GET failed for key %s: %v", key, err)
		} else {
			return "", redis.Nil // Not found
		}
	}

	if lastErr != nil {
		return "", lastErr
	}
	return "", redis.Nil // Not found
}

// GetBarcodeSeedDetailsByKeyID retrieves barcode seed details by keyID from cache
func (r *RedisCache) GetBarcodeSeedDetailsByKeyID(ctx context.Context, keyID string) (*model.BarcodeSeedDetails, error) {
	if !r.isEnabled() {
		return nil, nil // Cache disabled
	}

	key := fmt.Sprintf("barcode_seed:key:%s", keyID)
	val, err := r.getFromAvailableClients(ctx, key)
	if err == redis.Nil {
		return nil, nil // Not found in cache
	}
	if err != nil {
		log.Printf("Redis GET error for key %s: %v", key, err)
		return nil, nil // Return nil on error to fallback to database
	}

	var details model.BarcodeSeedDetails
	if err := json.Unmarshal([]byte(val), &details); err != nil {
		log.Printf("Redis JSON unmarshal error for key %s: %v", key, err)
		return nil, nil
	}

	log.Printf("Cache HIT: Found barcode seed details for keyID: %s", keyID)
	return &details, nil
}

// GetBarcodeSeedDetailsByEasyIDAndDeviceID retrieves barcode seed details by easyID and deviceID from cache
func (r *RedisCache) GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx context.Context, easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	if !r.isEnabled() {
		return nil, nil // Cache disabled
	}

	key := fmt.Sprintf("barcode_seed:user:%d_%s", easyID, deviceID)
	val, err := r.getFromAvailableClients(ctx, key)
	if err == redis.Nil {
		return nil, nil // Not found in cache
	}
	if err != nil {
		log.Printf("Redis GET error for key %s: %v", key, err)
		return nil, nil // Return nil on error to fallback to database
	}

	var details model.BarcodeSeedDetails
	if err := json.Unmarshal([]byte(val), &details); err != nil {
		log.Printf("Redis JSON unmarshal error for key %s: %v", key, err)
		return nil, nil
	}

	log.Printf("Cache HIT: Found barcode seed details for easyID: %d, deviceID: %s", easyID, deviceID)
	return &details, nil
}

// SetBarcodeSeedDetails stores barcode seed details in cache
func (r *RedisCache) SetBarcodeSeedDetails(ctx context.Context, details *model.BarcodeSeedDetails) error {
	if !r.isEnabled() {
		return nil // Cache disabled
	}

	data, err := json.Marshal(details)
	if err != nil {
		log.Printf("Redis JSON marshal error: %v", err)
		return err
	}

	// Store by keyID on all available clients
	keyByID := fmt.Sprintf("barcode_seed:key:%s", details.KeyID)
	err1 := r.executeOnAvailableClients(ctx, func(client *redis.Client) error {
		return client.Set(ctx, keyByID, data, r.ttl).Err()
	})

	// Store by easyID_deviceID on all available clients
	keyByUser := fmt.Sprintf("barcode_seed:user:%d_%s", details.EasyID, details.DeviceID)
	err2 := r.executeOnAvailableClients(ctx, func(client *redis.Client) error {
		return client.Set(ctx, keyByUser, data, r.ttl).Err()
	})

	// Return error if both operations failed
	if err1 != nil && err2 != nil {
		log.Printf("Redis SET failed for both keys: %v, %v", err1, err2)
		return err1
	}

	log.Printf("Cache SET: Stored barcode seed details for keyID: %s, easyID: %d, deviceID: %s",
		details.KeyID, details.EasyID, details.DeviceID)
	return nil
}

// DeleteBarcodeSeedDetails removes barcode seed details from cache
func (r *RedisCache) DeleteBarcodeSeedDetails(ctx context.Context, keyID string, easyID int64, deviceID string) error {
	if !r.isEnabled() {
		return nil // Cache disabled
	}

	keys := []string{
		fmt.Sprintf("barcode_seed:key:%s", keyID),
		fmt.Sprintf("barcode_seed:user:%d_%s", easyID, deviceID),
	}

	// Delete from all available clients
	for _, key := range keys {
		r.executeOnAvailableClients(ctx, func(client *redis.Client) error {
			return client.Del(ctx, key).Err()
		})
	}

	log.Printf("Cache DEL: Removed barcode seed details for keyID: %s, easyID: %d, deviceID: %s",
		keyID, easyID, deviceID)
	return nil
}

// Close closes the Redis connections and stops health monitoring
func (r *RedisCache) Close() error {
	// Stop health monitoring
	if r.stopHealthCheck != nil {
		select {
		case <-r.stopHealthCheck:
			// Already closed
		default:
			close(r.stopHealthCheck)
		}
		// Wait for health monitoring to stop
		if r.healthCheckDone != nil {
			<-r.healthCheckDone
		}
	}

	r.healthMutex.Lock()
	defer r.healthMutex.Unlock()

	var lastErr error

	if r.primaryClient != nil {
		if err := r.primaryClient.Close(); err != nil {
			lastErr = err
			log.Printf("Error closing primary Redis client: %v", err)
		}
		r.primaryClient = nil
		r.primaryHealthy = false
	}

	if r.secondaryClient != nil {
		if err := r.secondaryClient.Close(); err != nil {
			lastErr = err
			log.Printf("Error closing secondary Redis client: %v", err)
		}
		r.secondaryClient = nil
		r.secondaryHealthy = false
	}

	return lastErr
}

// NoOpCache is a no-operation cache implementation for when Redis is disabled
type NoOpCache struct{}

func NewNoOpCache() *NoOpCache {
	return &NoOpCache{}
}

func (n *NoOpCache) GetBarcodeSeedDetailsByKeyID(ctx context.Context, keyID string) (*model.BarcodeSeedDetails, error) {
	return nil, nil
}

func (n *NoOpCache) GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx context.Context, easyID int64, deviceID string) (*model.BarcodeSeedDetails, error) {
	return nil, nil
}

func (n *NoOpCache) SetBarcodeSeedDetails(ctx context.Context, details *model.BarcodeSeedDetails) error {
	return nil
}

func (n *NoOpCache) DeleteBarcodeSeedDetails(ctx context.Context, keyID string, easyID int64, deviceID string) error {
	return nil
}

func (n *NoOpCache) Close() error {
	return nil
}
