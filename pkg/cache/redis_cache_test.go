package cache

import (
	"context"
	"testing"
	"time"

	"rpay-barcode-service-go/pkg/model"
)

func TestNewRedisCache(t *testing.T) {
	// Test with invalid Redis addresses (should not panic)
	cache := NewRedisCache("invalid:6379", "invalid:6380", "", 0, 5*time.Minute)

	if cache == nil {
		t.Error("NewRedisCache() returned nil")
	}

	// Should handle connection failure gracefully
	if cache.primaryClient != nil || cache.secondaryClient != nil {
		t.Log("Redis connection succeeded (Redis server available)")
	} else {
		t.Log("Redis connection failed (Redis server not available) - cache disabled")
	}
}

func TestNewNoOpCache(t *testing.T) {
	cache := NewNoOpCache()

	if cache == nil {
		t.Error("NewNoOpCache() returned nil")
	}
}

func TestNoOpCache_Operations(t *testing.T) {
	cache := NewNoOpCache()
	ctx := context.Background()

	// Test all operations return nil/no-op
	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}

	// GetBarcodeSeedDetailsByKeyID should return nil
	result, err := cache.GetBarcodeSeedDetailsByKeyID(ctx, "test123")
	if err != nil {
		t.Errorf("GetBarcodeSeedDetailsByKeyID() error = %v", err)
	}
	if result != nil {
		t.Error("GetBarcodeSeedDetailsByKeyID() should return nil")
	}

	// GetBarcodeSeedDetailsByEasyIDAndDeviceID should return nil
	result, err = cache.GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx, 123456, "device123")
	if err != nil {
		t.Errorf("GetBarcodeSeedDetailsByEasyIDAndDeviceID() error = %v", err)
	}
	if result != nil {
		t.Error("GetBarcodeSeedDetailsByEasyIDAndDeviceID() should return nil")
	}

	// SetBarcodeSeedDetails should not error
	err = cache.SetBarcodeSeedDetails(ctx, details)
	if err != nil {
		t.Errorf("SetBarcodeSeedDetails() error = %v", err)
	}

	// DeleteBarcodeSeedDetails should not error
	err = cache.DeleteBarcodeSeedDetails(ctx, "test123", 123456, "device123")
	if err != nil {
		t.Errorf("DeleteBarcodeSeedDetails() error = %v", err)
	}

	// Close should not error
	err = cache.Close()
	if err != nil {
		t.Errorf("Close() error = %v", err)
	}
}

func TestRedisCache_DisabledClient(t *testing.T) {
	// Create cache with nil clients (simulates disabled cache)
	cache := &RedisCache{
		primaryClient:    nil,
		secondaryClient:  nil,
		ttl:              5 * time.Minute,
		primaryHealthy:   false,
		secondaryHealthy: false,
		stopHealthCheck:  nil, // Don't create channels for test
		healthCheckDone:  nil,
	}

	ctx := context.Background()

	details := &model.BarcodeSeedDetails{
		KeyID:    "test123",
		EasyID:   123456,
		DeviceID: "device123",
		OtpSeed:  "TESTSEED",
	}

	// All operations should handle nil client gracefully
	result, err := cache.GetBarcodeSeedDetailsByKeyID(ctx, "test123")
	if err != nil {
		t.Errorf("GetBarcodeSeedDetailsByKeyID() error = %v", err)
	}
	if result != nil {
		t.Error("GetBarcodeSeedDetailsByKeyID() should return nil when client is disabled")
	}

	result, err = cache.GetBarcodeSeedDetailsByEasyIDAndDeviceID(ctx, 123456, "device123")
	if err != nil {
		t.Errorf("GetBarcodeSeedDetailsByEasyIDAndDeviceID() error = %v", err)
	}
	if result != nil {
		t.Error("GetBarcodeSeedDetailsByEasyIDAndDeviceID() should return nil when client is disabled")
	}

	err = cache.SetBarcodeSeedDetails(ctx, details)
	if err != nil {
		t.Errorf("SetBarcodeSeedDetails() error = %v", err)
	}

	err = cache.DeleteBarcodeSeedDetails(ctx, "test123", 123456, "device123")
	if err != nil {
		t.Errorf("DeleteBarcodeSeedDetails() error = %v", err)
	}

	err = cache.Close()
	if err != nil {
		t.Errorf("Close() error = %v", err)
	}
}

func TestRedisCache_KeyGeneration(t *testing.T) {
	// Test that we can generate the expected cache keys
	tests := []struct {
		name     string
		keyID    string
		easyID   int64
		deviceID string
		expected []string
	}{
		{
			name:     "Standard case",
			keyID:    "12345678901",
			easyID:   123456,
			deviceID: "device123",
			expected: []string{
				"barcode_seed:key:12345678901",
				"barcode_seed:user:123456_device123",
			},
		},
		{
			name:     "Special characters in deviceID",
			keyID:    "98765432109",
			easyID:   654321,
			deviceID: "device_with_underscores",
			expected: []string{
				"barcode_seed:key:98765432109",
				"barcode_seed:user:654321_device_with_underscores",
			},
		},
		{
			name:     "Large easyID",
			keyID:    "11111111111",
			easyID:   9999999999,
			deviceID: "dev",
			expected: []string{
				"barcode_seed:key:11111111111",
				"barcode_seed:user:9999999999_dev",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// We can't easily test the actual key generation without exposing internal methods,
			// but we can verify the pattern is consistent by checking the expected format
			expectedKeyByID := "barcode_seed:key:" + tt.keyID

			// This is more of a documentation test to ensure we understand the key format
			if expectedKeyByID != tt.expected[0] {
				t.Errorf("Key by ID format mismatch: got %s, want %s", expectedKeyByID, tt.expected[0])
			}

			// Note: The actual key generation uses fmt.Sprintf which handles int64 properly
			// This test mainly documents the expected key format
		})
	}
}
