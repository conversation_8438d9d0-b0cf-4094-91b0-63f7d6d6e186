package config

import (
	"encoding/base64"
	"os"
	"strconv"
	"time"
)

type Config struct {
	// JWT Configuration
	RPAYSecretKey       []byte
	RPAYSubject         string
	BarcodeInternalRole string
	Scheme              string

	// Offline Barcode Generate Seed
	OpsLimit                 int
	MemLimit                 int
	Parallelism              int
	OtpSeedArrayLength       int
	KeyIdLength              int
	MinClientSecretExpiryDay int64
	MaxClientSecretExpiryDay int64
	OfflinePrefixLength      int
	SaltLength               int

	// Offline Barcode Validate OTP
	OfflineBarcodeExpireTime time.Duration
	TimeStep                 time.Duration
	TotpDigits               int

	// Server Configuration
	Port     string
	HTTPPort string

	// Redis Configuration
	RedisPrimaryAddr   string
	RedisSecondaryAddr string
	RedisPassword      string
	RedisDB            int
	RedisTTL           time.Duration
	EnableCache        bool

	// MySQL Database Configuration
	DatabaseDSN string
}

func LoadConfig() *Config {
	config := &Config{
		// Default values
		Port:                     getEnv("PORT", "8080"),
		HTTPPort:                 getEnv("HTTP_PORT", "8081"),
		RPAYSubject:              getEnv("RPAY_SUBJECT", "rpay"),
		BarcodeInternalRole:      getEnv("BARCODE_INTERNAL_ROLE", "barcode"),
		Scheme:                   getEnv("SCHEME", "Bearer "),
		OpsLimit:                 getEnvAsInt("OPS_LIMIT", 4),
		MemLimit:                 getEnvAsInt("MEM_LIMIT", 65536),
		Parallelism:              getEnvAsInt("PARALLELISM", 1),
		OtpSeedArrayLength:       getEnvAsInt("OTP_SEED_ARRAY_LENGTH", 20),
		KeyIdLength:              getEnvAsInt("KEY_ID_LENGTH", 11),
		MinClientSecretExpiryDay: getEnvAsInt64("MIN_CLIENT_SECRET_EXPIRY_DAY", 5),
		MaxClientSecretExpiryDay: getEnvAsInt64("MAX_CLIENT_SECRET_EXPIRY_DAY", 30),
		OfflinePrefixLength:      getEnvAsInt("OFFLINE_PREFIX_LENGTH", 11),
		SaltLength:               getEnvAsInt("SALT_LENGTH", 16),
		OfflineBarcodeExpireTime: time.Duration(getEnvAsInt("OFFLINE_BARCODE_EXPIRE_TIME", 60)) * time.Second,
		TimeStep:                 time.Duration(getEnvAsInt("TIME_STEP", 60)) * time.Second,
		TotpDigits:               getEnvAsInt("TOTP_DIGITS", 6),

		// Redis configuration
		RedisPrimaryAddr:   getEnv("REDIS_PRIMARY_ADDR", "localhost:6379"),
		RedisSecondaryAddr: getEnv("REDIS_SECONDARY_ADDR", ""),
		RedisPassword:      getEnv("REDIS_PASSWORD", ""),
		RedisDB:            getEnvAsInt("REDIS_DB", 0),
		RedisTTL:           time.Duration(getEnvAsInt("REDIS_TTL_MINUTES", 30)) * time.Minute,
		EnableCache:        getEnv("ENABLE_CACHE", "true") == "true",

		// MySQL database configuration
		DatabaseDSN: getEnv("DATABASE_DSN", "root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true"),
	}

	// Decode JWT secret key
	secretKeyStr := getEnv("RPAY_SECRET_KEY", "dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU=")
	secretKey, err := base64.StdEncoding.DecodeString(secretKeyStr)
	if err != nil {
		panic("Failed to decode RPAY_SECRET_KEY: " + err.Error())
	}
	config.RPAYSecretKey = secretKey

	return config
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}
