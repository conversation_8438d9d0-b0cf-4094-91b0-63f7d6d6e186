package config

import (
	"encoding/base64"
	"os"
	"testing"
	"time"
)

func TestLoadConfig(t *testing.T) {
	// Save original environment
	originalEnv := make(map[string]string)
	envVars := []string{
		"PORT", "HTTP_PORT", "RPAY_SECRET_KEY", "RPAY_SUBJECT",
		"BARCODE_INTERNAL_ROLE", "SCHEME", "OPS_LIMIT", "MEM_LIMIT",
		"PARALLELISM", "OTP_SEED_ARRAY_LENGTH", "KEY_ID_LENGTH",
		"MIN_CLIENT_SECRET_EXPIRY_DAY", "MAX_CLIENT_SECRET_EXPIRY_DAY",
		"OFFLINE_PREFIX_LENGTH", "SALT_LENGTH", "OFFLINE_BARCODE_EXPIRE_TIME",
		"TIME_STEP",
	}

	for _, env := range envVars {
		originalEnv[env] = os.Getenv(env)
	}

	// Clean environment
	for _, env := range envVars {
		os.Unsetenv(env)
	}

	defer func() {
		// Restore original environment
		for env, value := range originalEnv {
			if value != "" {
				os.Setenv(env, value)
			} else {
				os.Unsetenv(env)
			}
		}
	}()

	t.Run("Default values", func(t *testing.T) {
		cfg := LoadConfig()

		if cfg.Port != "8080" {
			t.Errorf("Port = %s, want 8080", cfg.Port)
		}

		if cfg.HTTPPort != "8081" {
			t.Errorf("HTTPPort = %s, want 8081", cfg.HTTPPort)
		}

		if cfg.RPAYSubject != "rpay" {
			t.Errorf("RPAYSubject = %s, want rpay", cfg.RPAYSubject)
		}

		if cfg.BarcodeInternalRole != "barcode" {
			t.Errorf("BarcodeInternalRole = %s, want barcode", cfg.BarcodeInternalRole)
		}

		if cfg.Scheme != "Bearer " {
			t.Errorf("Scheme = %s, want 'Bearer '", cfg.Scheme)
		}

		if cfg.OpsLimit != 4 {
			t.Errorf("OpsLimit = %d, want 4", cfg.OpsLimit)
		}

		if cfg.MemLimit != 65536 {
			t.Errorf("MemLimit = %d, want %d", cfg.MemLimit, 65536)
		}

		if cfg.Parallelism != 1 {
			t.Errorf("Parallelism = %d, want 1", cfg.Parallelism)
		}

		if cfg.OtpSeedArrayLength != 20 {
			t.Errorf("OtpSeedArrayLength = %d, want 20", cfg.OtpSeedArrayLength)
		}

		if cfg.KeyIdLength != 11 {
			t.Errorf("KeyIdLength = %d, want 11", cfg.KeyIdLength)
		}

		if cfg.MinClientSecretExpiryDay != 5 {
			t.Errorf("MinClientSecretExpiryDay = %d, want 5", cfg.MinClientSecretExpiryDay)
		}

		if cfg.MaxClientSecretExpiryDay != 30 {
			t.Errorf("MaxClientSecretExpiryDay = %d, want 30", cfg.MaxClientSecretExpiryDay)
		}

		if cfg.OfflinePrefixLength != 11 {
			t.Errorf("OfflinePrefixLength = %d, want 11", cfg.OfflinePrefixLength)
		}

		if cfg.SaltLength != 16 {
			t.Errorf("SaltLength = %d, want 16", cfg.SaltLength)
		}

		if cfg.OfflineBarcodeExpireTime != 60*time.Second {
			t.Errorf("OfflineBarcodeExpireTime = %v, want %v", cfg.OfflineBarcodeExpireTime, 60*time.Second)
		}

		if cfg.TimeStep != 60*time.Second {
			t.Errorf("TimeStep = %v, want %v", cfg.TimeStep, 60*time.Second)
		}
	})

	t.Run("Custom environment values", func(t *testing.T) {
		// Set custom environment values
		os.Setenv("PORT", "9090")
		os.Setenv("HTTP_PORT", "9091")
		os.Setenv("RPAY_SECRET_KEY", base64.StdEncoding.EncodeToString([]byte("test-secret-key")))
		os.Setenv("RPAY_SUBJECT", "test-subject")
		os.Setenv("BARCODE_INTERNAL_ROLE", "test-role")
		os.Setenv("SCHEME", "Token ")
		os.Setenv("OPS_LIMIT", "5")
		os.Setenv("MEM_LIMIT", "65536")
		os.Setenv("PARALLELISM", "8")
		os.Setenv("OTP_SEED_ARRAY_LENGTH", "64")
		os.Setenv("KEY_ID_LENGTH", "15")
		os.Setenv("MIN_CLIENT_SECRET_EXPIRY_DAY", "3")
		os.Setenv("MAX_CLIENT_SECRET_EXPIRY_DAY", "10")
		os.Setenv("OFFLINE_PREFIX_LENGTH", "8")
		os.Setenv("SALT_LENGTH", "32")
		os.Setenv("OFFLINE_BARCODE_EXPIRE_TIME", "600")
		os.Setenv("TIME_STEP", "60")

		cfg := LoadConfig()

		if cfg.Port != "9090" {
			t.Errorf("Port = %s, want 9090", cfg.Port)
		}

		if cfg.HTTPPort != "9091" {
			t.Errorf("HTTPPort = %s, want 9091", cfg.HTTPPort)
		}

		expectedSecretKey := []byte("test-secret-key")
		if string(cfg.RPAYSecretKey) != string(expectedSecretKey) {
			t.Errorf("RPAYSecretKey = %v, want %v", cfg.RPAYSecretKey, expectedSecretKey)
		}

		if cfg.RPAYSubject != "test-subject" {
			t.Errorf("RPAYSubject = %s, want test-subject", cfg.RPAYSubject)
		}

		if cfg.BarcodeInternalRole != "test-role" {
			t.Errorf("BarcodeInternalRole = %s, want test-role", cfg.BarcodeInternalRole)
		}

		if cfg.Scheme != "Token " {
			t.Errorf("Scheme = %s, want 'Token '", cfg.Scheme)
		}

		if cfg.OpsLimit != 5 {
			t.Errorf("OpsLimit = %d, want 5", cfg.OpsLimit)
		}

		if cfg.MemLimit != 65536 {
			t.Errorf("MemLimit = %d, want 65536", cfg.MemLimit)
		}

		if cfg.Parallelism != 8 {
			t.Errorf("Parallelism = %d, want 8", cfg.Parallelism)
		}

		if cfg.OtpSeedArrayLength != 64 {
			t.Errorf("OtpSeedArrayLength = %d, want 64", cfg.OtpSeedArrayLength)
		}

		if cfg.KeyIdLength != 15 {
			t.Errorf("KeyIdLength = %d, want 15", cfg.KeyIdLength)
		}

		if cfg.MinClientSecretExpiryDay != 3 {
			t.Errorf("MinClientSecretExpiryDay = %d, want 3", cfg.MinClientSecretExpiryDay)
		}

		if cfg.MaxClientSecretExpiryDay != 10 {
			t.Errorf("MaxClientSecretExpiryDay = %d, want 10", cfg.MaxClientSecretExpiryDay)
		}

		if cfg.OfflinePrefixLength != 8 {
			t.Errorf("OfflinePrefixLength = %d, want 8", cfg.OfflinePrefixLength)
		}

		if cfg.SaltLength != 32 {
			t.Errorf("SaltLength = %d, want 32", cfg.SaltLength)
		}

		if cfg.OfflineBarcodeExpireTime != 600*time.Second {
			t.Errorf("OfflineBarcodeExpireTime = %v, want %v", cfg.OfflineBarcodeExpireTime, 600*time.Second)
		}

		if cfg.TimeStep != 60*time.Second {
			t.Errorf("TimeStep = %v, want %v", cfg.TimeStep, 60*time.Second)
		}
	})
}

func TestGetEnv(t *testing.T) {
	tests := []struct {
		name         string
		key          string
		defaultValue string
		envValue     string
		expected     string
	}{
		{
			name:         "Environment variable exists",
			key:          "TEST_ENV_VAR",
			defaultValue: "default",
			envValue:     "custom",
			expected:     "custom",
		},
		{
			name:         "Environment variable doesn't exist",
			key:          "NON_EXISTENT_VAR",
			defaultValue: "default",
			envValue:     "",
			expected:     "default",
		},
		{
			name:         "Empty environment variable",
			key:          "EMPTY_VAR",
			defaultValue: "default",
			envValue:     "",
			expected:     "default",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up
			os.Unsetenv(tt.key)

			if tt.envValue != "" {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnv(tt.key, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("getEnv(%s, %s) = %s, want %s", tt.key, tt.defaultValue, result, tt.expected)
			}
		})
	}
}

func TestGetEnvAsInt(t *testing.T) {
	tests := []struct {
		name         string
		key          string
		defaultValue int
		envValue     string
		expected     int
	}{
		{
			name:         "Valid integer",
			key:          "TEST_INT_VAR",
			defaultValue: 10,
			envValue:     "25",
			expected:     25,
		},
		{
			name:         "Invalid integer",
			key:          "INVALID_INT_VAR",
			defaultValue: 10,
			envValue:     "not_a_number",
			expected:     10,
		},
		{
			name:         "Empty environment variable",
			key:          "EMPTY_INT_VAR",
			defaultValue: 10,
			envValue:     "",
			expected:     10,
		},
		{
			name:         "Zero value",
			key:          "ZERO_VAR",
			defaultValue: 10,
			envValue:     "0",
			expected:     0,
		},
		{
			name:         "Negative value",
			key:          "NEGATIVE_VAR",
			defaultValue: 10,
			envValue:     "-5",
			expected:     -5,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up
			os.Unsetenv(tt.key)

			if tt.envValue != "" {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnvAsInt(tt.key, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("getEnvAsInt(%s, %d) = %d, want %d", tt.key, tt.defaultValue, result, tt.expected)
			}
		})
	}
}

func TestGetEnvAsInt64(t *testing.T) {
	tests := []struct {
		name         string
		key          string
		defaultValue int64
		envValue     string
		expected     int64
	}{
		{
			name:         "Valid int64",
			key:          "TEST_INT64_VAR",
			defaultValue: 100,
			envValue:     "9223372036854775807", // Max int64
			expected:     9223372036854775807,
		},
		{
			name:         "Invalid int64",
			key:          "INVALID_INT64_VAR",
			defaultValue: 100,
			envValue:     "not_a_number",
			expected:     100,
		},
		{
			name:         "Empty environment variable",
			key:          "EMPTY_INT64_VAR",
			defaultValue: 100,
			envValue:     "",
			expected:     100,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Clean up
			os.Unsetenv(tt.key)

			if tt.envValue != "" {
				os.Setenv(tt.key, tt.envValue)
				defer os.Unsetenv(tt.key)
			}

			result := getEnvAsInt64(tt.key, tt.defaultValue)
			if result != tt.expected {
				t.Errorf("getEnvAsInt64(%s, %d) = %d, want %d", tt.key, tt.defaultValue, result, tt.expected)
			}
		})
	}
}
