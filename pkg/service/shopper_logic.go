package service

import (
	"fmt"
	"log"
	"time"

	"rpay-barcode-service-go/pkg/config"
	"rpay-barcode-service-go/pkg/database"
	"rpay-barcode-service-go/pkg/generator"
	"rpay-barcode-service-go/pkg/model"
	pb "rpay-barcode-service-go/proto"
)

type ShopperLogic struct {
	config                *config.Config
	db                    database.DatabaseInterface
	keyIdGenerator        *generator.KeyIdGenerator
	otpSeedGenerator      *generator.OtpSeedGenerator
	totpGenerator         *generator.TotpGenerator
	barcodeTokenGenerator *generator.BarcodeTokenGenerator
}

func NewShopperLogic(cfg *config.Config, db database.DatabaseInterface) *ShopperLogic {
	return &ShopperLogic{
		config:                cfg,
		db:                    db,
		keyIdGenerator:        generator.NewKeyIdGenerator(),
		otpSeedGenerator:      generator.NewOtpSeedGenerator(cfg.OpsLimit, cfg.MemLimit, cfg.Parallelism, cfg.SaltLength),
		totpGenerator:         generator.NewTotpGenerator(),
		barcodeTokenGenerator: generator.NewBarcodeTokenGenerator(),
	}
}

func (s *ShopperLogic) GenerateOfflineDetails(easyID int64, deviceID, sessionID, ipAddress string) (*pb.BarcodeGenerateSeedResponse, error) {
	// Check if existing record exists
	existingDetails, err := s.db.FindBarcodeSeedDetailsByEasyIDAndDeviceID(easyID, deviceID)
	if err != nil {
		return nil, fmt.Errorf("failed to find existing barcode seed details: %w", err)
	}

	now := time.Now()

	if existingDetails != nil {
		// Check if we need to update (past minimum expiry)
		if now.After(existingDetails.MinExpiryDateTime) {
			// Update existing record
			keyID, err := s.keyIdGenerator.GenerateKeyId(sessionID, easyID, s.config.KeyIdLength)
			if err != nil {
				return nil, fmt.Errorf("failed to generate key ID: %w", err)
			}

			otpSeed, err := s.otpSeedGenerator.GenerateArgon2Hash(sessionID, easyID, s.config.OtpSeedArrayLength)
			if err != nil {
				return nil, fmt.Errorf("failed to generate OTP seed: %w", err)
			}

			existingDetails.KeyID = keyID
			existingDetails.OtpSeed = otpSeed
			existingDetails.UpdateDateTime = now
			existingDetails.MinExpiryDateTime = now.AddDate(0, 0, int(s.config.MinClientSecretExpiryDay))
			existingDetails.MaxExpiryDateTime = now.AddDate(0, 0, int(s.config.MaxClientSecretExpiryDay))

			err = s.db.UpdateBarcodeSeedDetails(existingDetails)
			if err != nil {
				return nil, fmt.Errorf("failed to update barcode seed details: %w", err)
			}

			return s.toBarcodeGenerateSeedResponse(existingDetails), nil
		} else {
			// Return existing record
			return s.toBarcodeGenerateSeedResponse(existingDetails), nil
		}
	} else {
		// Create new record
		keyID, err := s.keyIdGenerator.GenerateKeyId(sessionID, easyID, s.config.KeyIdLength)
		if err != nil {
			return nil, fmt.Errorf("failed to generate key ID: %w", err)
		}

		otpSeed, err := s.otpSeedGenerator.GenerateArgon2Hash(sessionID, easyID, s.config.OtpSeedArrayLength)
		if err != nil {
			return nil, fmt.Errorf("failed to generate OTP seed: %w", err)
		}

		newDetails := &model.BarcodeSeedDetails{
			EasyID:            easyID,
			DeviceID:          deviceID,
			IPAddress:         ipAddress,
			KeyID:             keyID,
			OtpSeed:           otpSeed,
			DeletedFlag:       model.DeletedFlagNotDeleted,
			CreateDateTime:    now,
			UpdateDateTime:    now,
			MinExpiryDateTime: now.AddDate(0, 0, int(s.config.MinClientSecretExpiryDay)),
			MaxExpiryDateTime: now.AddDate(0, 0, int(s.config.MaxClientSecretExpiryDay)),
		}

		err = s.db.CreateBarcodeSeedDetails(newDetails)
		if err != nil {
			return nil, fmt.Errorf("failed to create barcode seed details: %w", err)
		}

		return s.toBarcodeGenerateSeedResponse(newDetails), nil
	}
}

func (s *ShopperLogic) VerifyOtpForOfflineBarcode(offlineBarcode string) (*pb.BarcodeVerifyOtpResponse, error) {
	// Split the offline barcode
	barcodeSplit := s.splitOfflineBarcode(offlineBarcode)
	totp := barcodeSplit.Totp

	log.Printf("Verifying OTP for offline barcode: %+v", barcodeSplit)

	// Find barcode seed details by keyID
	details, err := s.db.FindBarcodeSeedDetailsByKeyID(barcodeSplit.KeyID)
	if err != nil {
		return nil, fmt.Errorf("failed to find barcode seed details: %w", err)
	}

	if details == nil {
		log.Printf("No barcode seed details found for keyID: %s", barcodeSplit.KeyID)
		return &pb.BarcodeVerifyOtpResponse{Status: false}, nil
	}

	// Generate current and previous TOTP
	now := time.Now()
	previousTime := now.Add(-s.config.OfflineBarcodeExpireTime)

	currentOtp, err := s.totpGenerator.GenerateTotp(details.OtpSeed, now, s.config.TimeStep, s.config.TotpDigits)
	if err != nil {
		return nil, fmt.Errorf("failed to generate current TOTP: %w", err)
	}

	previousOtp, err := s.totpGenerator.GenerateTotp(details.OtpSeed, previousTime, s.config.TimeStep, s.config.TotpDigits)
	if err != nil {
		return nil, fmt.Errorf("failed to generate previous TOTP: %w", err)
	}

	// Verify TOTP
	if totp != currentOtp && totp != previousOtp {
		log.Printf("TOTP verification failed. Expected: %s or %s, Got: %s", currentOtp, previousOtp, totp)
		return &pb.BarcodeVerifyOtpResponse{Status: false}, nil
	}

	log.Printf("TOTP verified successfully")

	// Generate barcode token
	barcodeToken := s.barcodeTokenGenerator.GenerateBarcodeToken(details.EasyID, now)
	barcode := s.createBarcodeString(barcodeToken)

	log.Printf("Generated barcode: %s", barcode)

	// Create BarcodeForShopper record
	bfs := s.createBarcodeForShopper(details.EasyID, details.DeviceID, details.IPAddress, barcode, now)
	barcodeID, err := s.db.CreateBarcodeForShopper(bfs)
	if err != nil {
		return nil, fmt.Errorf("failed to create barcode for shopper: %w", err)
	}

	log.Printf("Created BarcodeForShopper with ID: %d", barcodeID)

	// Create OfflineBarcodeTransaction record
	obt := &model.OfflineBarcodeTransaction{
		OfflineBarcode: barcode,
		BarcodeID:      barcodeID,
		PointUsage:     model.PointUsageUsePartOff,
		DeviceID:       details.DeviceID,
		EasyID:         details.EasyID,
		KeyID:          details.KeyID,
		DeletedFlag:    model.DeletedFlagNotDeleted,
		CreatedDate:    now,
	}

	obtID, err := s.db.CreateOfflineBarcodeTransaction(obt)
	if err != nil {
		return nil, fmt.Errorf("failed to create offline barcode transaction: %w", err)
	}

	log.Printf("Created OfflineBarcodeTransaction with ID: %d", obtID)

	return &pb.BarcodeVerifyOtpResponse{
		Status:    true,
		Barcode:   &barcode,
		BarcodeId: &barcodeID,
		EasyId:    &details.EasyID,
		DeviceId:  &details.DeviceID,
		IpAddress: &details.IPAddress,
	}, nil
}

func (s *ShopperLogic) splitOfflineBarcode(offlineBarcode string) *model.BarcodeSplit {
	if len(offlineBarcode) < 18 {
		return &model.BarcodeSplit{}
	}

	return &model.BarcodeSplit{
		JpqrCodePrefix:    offlineBarcode[0:4],
		JpqrRpayPrefix:    offlineBarcode[4:6],
		BarcodeIdentifier: offlineBarcode[6:7],
		KeyID:             offlineBarcode[7:18],
		Totp:              offlineBarcode[18:],
	}
}

func (s *ShopperLogic) createBarcodeString(barcodeToken string) string {
	return model.JPQROfflineCodePrefix + model.JPQRRpayIDPrefix + barcodeToken
}

func (s *ShopperLogic) createBarcodeForShopper(easyID int64, deviceID, ipAddress, barcode string, now time.Time) *model.BarcodeForShopper {
	return &model.BarcodeForShopper{
		Barcode:            barcode,
		EasyID:             easyID,
		PointUsage:         model.PointUsageUsePartOff,
		PointPaymentAmount: 0,
		IPAddress:          ipAddress,
		MailHash:           "",
		DeviceID:           deviceID,
		ExpireDatetime:     now.Add(10 * time.Minute),
		Scanned:            model.ScannedScanned,
		DeletedFlag:        model.DeletedFlagNotDeleted,
		CreateDatetime:     now,
		UpdateDatetime:     now,
		Operator:           model.OperatorWeb,
		Timestamp:          now,
	}
}

func (s *ShopperLogic) toBarcodeGenerateSeedResponse(details *model.BarcodeSeedDetails) *pb.BarcodeGenerateSeedResponse {
	return &pb.BarcodeGenerateSeedResponse{
		KeyId:         details.KeyID,
		OtpSeed:       details.OtpSeed,
		MinExpiryDate: details.MinExpiryDateTime.Format(time.RFC3339),
	}
}
