package service

import (
	"context"
	"log"

	pb "rpay-barcode-service-go/proto"
)

// ShopperBarcodeServiceServer implements the ShopperBarcodeService
type ShopperBarcodeServiceServer struct {
	pb.UnimplementedShopperBarcodeServiceServer
	shopperLogic *ShopperLogic
}

func NewShopperBarcodeServiceServer(shopperLogic *ShopperLogic) *ShopperBarcodeServiceServer {
	return &ShopperBarcodeServiceServer{
		shopperLogic: shopperLogic,
	}
}

func (s *ShopperBarcodeServiceServer) GetShopperBarcodeGenerateSeed(
	ctx context.Context,
	req *pb.BarcodeGenerateSeedRequest,
) (*pb.BarcodeGenerateSeedResponse, error) {
	log.Printf("Received GetShopperBarcodeGenerateSeed request: easyId=%d, deviceId=%s, sessionId=%s, ipAddress=%s",
		req.EasyId, req.DeviceId, req.SessionId, req.IpAddress)

	response, err := s.shopperLogic.GenerateOfflineDetails(
		req.EasyId,
		req.DeviceId,
		req.SessionId,
		req.IpAddress,
	)
	if err != nil {
		log.Printf("Error generating offline details: %v", err)
		return nil, err
	}

	log.Printf("Generated seed response: keyId=%s, minExpiryDate=%s",
		response.KeyId, response.MinExpiryDate)

	return response, nil
}

// ShopperBarcodeInternalServiceServer implements the ShopperBarcodeInternalService
type ShopperBarcodeInternalServiceServer struct {
	pb.UnimplementedShopperBarcodeInternalServiceServer
	shopperLogic *ShopperLogic
}

func NewShopperBarcodeInternalServiceServer(shopperLogic *ShopperLogic) *ShopperBarcodeInternalServiceServer {
	return &ShopperBarcodeInternalServiceServer{
		shopperLogic: shopperLogic,
	}
}

func (s *ShopperBarcodeInternalServiceServer) GetShopperBarcodeVerifyOtp(
	ctx context.Context,
	req *pb.BarcodeVerifyOtpRequest,
) (*pb.BarcodeVerifyOtpResponse, error) {
	log.Printf("Received GetShopperBarcodeVerifyOtp request: offlineBarcode=%s", req.OfflineBarcode)

	response, err := s.shopperLogic.VerifyOtpForOfflineBarcode(req.OfflineBarcode)
	if err != nil {
		log.Printf("Error verifying OTP for offline barcode: %v", err)
		return nil, err
	}

	log.Printf("OTP verification response: status=%t", response.Status)

	return response, nil
}
