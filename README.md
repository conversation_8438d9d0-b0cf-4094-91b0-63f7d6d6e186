# RPay Barcode Service - Go Implementation

### Usage

```bash
go mod tidy
```

```bash
cp .env.example .env
# Edit .env with your configuration
```

```bash
# Start MySQL database
make mysql-start

# Start dual Redis cache instances (optional)
make redis-start
# This starts:
# - Primary Redis:   localhost:6379
# - Secondary Redis: localhost:6380
```

### Running the Server

```bash
# Using make command
make run-dev

# Or directly
go run cmd/server/main.go
```

This will start:
- **gRPC Server** on port 8080
- **HTTP Server** on port 8081 (with Swagger UI)

**Swagger UI**: Visit http://localhost:8081 to access the interactive API documentation.

**Health Check**: Visit http://localhost:8081/health to check server status.

### Testing with grpcurl

You can test the services using grpcurl:

```bash
# List services
grpcurl -plaintext localhost:8080 list

# Generate barcode seed (requires JWT token)
grpcurl -plaintext \
  -H "authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"easyId": 123456, "deviceId": "device123", "sessionId": "session123", "ipAddress": "***********"}' \
  localhost:8080 ShopperBarcodeService/GetShopperBarcodeGenerateSeed

# Verify OTP (requires JWT token with barcode role)
grpcurl -plaintext \
  -H "authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"offlineBarcode": "800001123456789012345678"}' \
  localhost:8080 ShopperBarcodeInternalService/GetShopperBarcodeVerifyOtp
```
