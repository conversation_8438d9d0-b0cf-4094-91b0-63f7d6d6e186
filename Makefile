.PHONY: build run clean proto test

# Build the server binary
build:
	go build -o bin/server cmd/server/main.go

# Run the server
run: build
	./bin/server

# Clean build artifacts
clean:
	rm -rf bin/

# Generate protobuf code
proto:
	protoc --go_out=. --go_opt=paths=source_relative \
		--go-grpc_out=. --go-grpc_opt=paths=source_relative \
		proto/*.proto

# Run tests
test:
	go test ./pkg/... -v

# Run tests with coverage
test-coverage:
	go test ./pkg/... -cover

# Run benchmarks
benchmark:
	go test ./pkg/... -bench=.

# Install dependencies
deps:
	go mod tidy

# Format code
fmt:
	go fmt ./...

# Run linter
lint:
	golangci-lint run

# Build for production
build-prod:
	CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/server cmd/server/main.go

# Run dual-mode server (gRPC + HTTP) with environment variables
run-dev:
	export PORT=8080 && \
	export HTTP_PORT=8081 && \
	export RPAY_SECRET_KEY=dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU= && \
	export RPAY_SUBJECT=rpay && \
	export BARCODE_INTERNAL_ROLE=barcode && \
	export SCHEME="Bearer " && \
	go run cmd/server/main.go

# Legacy: Run gRPC server only (deprecated - use run-dev instead)
run-grpc-dev:
	export PORT=8080 && \
	export RPAY_SECRET_KEY=dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU= && \
	export RPAY_SUBJECT=rpay && \
	export BARCODE_INTERNAL_ROLE=barcode && \
	export SCHEME="Bearer " && \
	./bin/server

# Test HTTP server
test-http:
	go run test_http_client.go

# Generate Swagger documentation
swagger:
	swag init -g cmd/server/main.go -o docs/

# Start dual Redis servers for redundancy (requires Docker)
redis-start:
	docker run --name rpay-redis-primary -p 6379:6379 -d redis:7-alpine
	docker run --name rpay-redis-secondary -p 6380:6379 -d redis:7-alpine
	@echo "Started dual Redis servers:"
	@echo "  Primary:   localhost:6379"
	@echo "  Secondary: localhost:6380"

# Stop Redis servers
redis-stop:
	-docker stop rpay-redis-primary rpay-redis-secondary
	-docker rm rpay-redis-primary rpay-redis-secondary

# Redis CLI for primary server
redis-cli:
	docker exec -it rpay-redis-primary redis-cli

# Redis CLI for secondary server
redis-cli-secondary:
	docker exec -it rpay-redis-secondary redis-cli

# Start MySQL server (requires Docker)
mysql-start:
	docker run --name rpay-mysql -p 3307:3306 -e MYSQL_ROOT_PASSWORD=mypass -e TZ=Asia/Tokyo -v $(PWD)/../setup/docker/mysql/initial_dump.sql:/docker-entrypoint-initdb.d/schema.sql:ro -d mysql:5.7.25

# Stop MySQL server
mysql-stop:
	docker stop rpay-mysql && docker rm rpay-mysql

# MySQL CLI
mysql-cli:
	docker exec -it rpay-mysql mysql -u root -pmypass RKPAYADMIN

help:
	@echo "Available targets:"
	@echo "  build        - Build the server binary"
	@echo "  run          - Build and run the server"
	@echo "  run-dev      - Run dual-mode server (gRPC + HTTP) with development environment variables"
	@echo "  run-grpc-dev - Run gRPC server only (deprecated - use run-dev instead)"
	@echo "  test-http    - Test HTTP server with sample client"
	@echo "  clean        - Clean build artifacts"
	@echo "  proto        - Generate protobuf code"
	@echo "  test         - Run tests"
	@echo "  test-coverage- Run tests with coverage"
	@echo "  benchmark    - Run benchmarks"
	@echo "  deps         - Install dependencies"
	@echo "  fmt          - Format code"
	@echo "  lint         - Run linter"
	@echo "  build-prod   - Build for production"
	@echo "  swagger      - Generate Swagger documentation"
	@echo "  redis-start  - Start dual Redis servers (Docker)"
	@echo "  redis-stop   - Stop Redis servers"
	@echo "  redis-cli    - Connect to primary Redis CLI"
	@echo "  redis-cli-secondary - Connect to secondary Redis CLI"
	@echo "  mysql-start  - Start MySQL server (Docker)"
	@echo "  mysql-stop   - Stop MySQL server"
	@echo "  mysql-cli    - Connect to MySQL CLI"
	@echo "  help         - Show this help message"
