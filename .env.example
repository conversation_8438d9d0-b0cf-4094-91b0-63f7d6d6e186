# Server Configuration
PORT=8080

# JWT Configuration
RPAY_SECRET_KEY=dzlrQmJCTUN5NXZ4N0xoaUloeHBmV2RFaHF5T3lhUXU=
RPAY_SUBJECT=rpay
BARCODE_INTERNAL_ROLE=barcode
SCHEME=Bearer

# Redis Configuration (Dual endpoints for redundancy)
REDIS_PRIMARY_ADDR=localhost:6379
REDIS_SECONDARY_ADDR=localhost:6380
REDIS_PASSWORD=
REDIS_DB=0
REDIS_TTL_MINUTES=30
ENABLE_CACHE=true

# MySQL Database Configuration
DATABASE_DSN=root:mypass@tcp(localhost:3307)/RKPAYADMIN?parseTime=true

# Offline Barcode Generate Seed Configuration
OPS_LIMIT=4
MEM_LIMIT=65536
PARALLELISM=1
OTP_SEED_ARRAY_LENGTH=20
KEY_ID_LENGTH=11
MIN_CLIENT_SECRET_EXPIRY_DAY=5
MAX_CLIENT_SECRET_EXPIRY_DAY=30
OFFLINE_PREFIX_LENGTH=11
SALT_LENGTH=16

# Offline Barcode Validate OTP Configuration
OFFLINE_BARCODE_EXPIRE_TIME=60
TIME_STEP=60
TOTP_DIGITS=6
